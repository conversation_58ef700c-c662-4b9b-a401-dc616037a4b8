<body onload="document.MainForm.torihikisaki_cd.focus()" bgcolor="#FFFFFF" text="#000000" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" >
<form name="MainForm" method="post" action="app">
<table width="100%" border="0" cellspacing="0" cellpadding="0">
	<tr>
		<td align="center" valign="top">
			<br>
			<div class="header_sub">代替商品検索</div>
			<input type="hidden" name="hachu_dt" value="<%=daitaiTable.getParameter("hachu_dt")%>">
			<input type="hidden" name="nohin_dt" value="<%=daitaiTable.getParameter("nohin_dt")%>">
			<input type="hidden" name="syohin_cd" value="<%=daitaiTable.getParameter("syohin_cd")%>">

			<div class="term_sub">
				<table class="term_sub" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<th nowrap>*商品コード</th>
						<td nowrap>
							<input type="text" name="term_syohinCd" align="middle" maxlength="20" value="<%=MDWareHTMLUtility.toText(daitaiTable.getParameter("syohin_cd"))%>" id="no_input_text" tabindex="-1" readonly>
						</td>
						<th nowrap></th>
						<td nowrap></td>
					</tr>
				</table>
				<table class="term_sub" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<th nowrap>取引先コード</th>
						<td nowrap>
							<input type="text" name="term_torihikisaki_cd" align="middle" maxlength="6" value="<%=MDWareHTMLUtility.toText(daitaiTable.getParameter("term_torihikisaki_cd"))%>" tabIndex="<%= tabIndex++ %>">
						</td>
						<th nowrap>取引先名称</th>
						<td nowrap>
							<input type="text" name="term_torihikisaki_na" align="middle" maxlength="20" value="<%=MDWareHTMLUtility.toText(daitaiTable.getParameter("term_torihikisaki_na"))%>" tabIndex="<%= tabIndex++ %>">
						</td>
					</tr>
				</table>
				<table class="term_sub" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<th nowrap>産地コード</th>
						<td nowrap>
							<input type="text" name="term_santi_cd" align="middle" maxlength="6" value="<%=MDWareHTMLUtility.toText(daitaiTable.getParameter("term_santi_cd"))%>" tabIndex="<%= tabIndex++ %>">
						</td>
						<th nowrap>産地名称</th>
						<td nowrap>
							<input type="text" name="term_santi_na" align="middle" maxlength="20" value="<%=MDWareHTMLUtility.toText(daitaiTable.getParameter("term_santi_na"))%>" tabIndex="<%= tabIndex++ %>">
						</td>
					</tr>
				</table>
				<table class="term_sub" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<th nowrap>等級コード</th>
						<td nowrap>
							<input type="text" name="term_tokaikyu_cd" align="middle" maxlength="3" value="<%=MDWareHTMLUtility.toText(daitaiTable.getParameter("term_tokaikyu_cd"))%>" tabIndex="<%= tabIndex++ %>">
						</td>
						<th nowrap>等級名称</th>
						<td nowrap>
							<input type="text" name="term_tokaikyu_na" align="middle" maxlength="10" value="<%=MDWareHTMLUtility.toText(daitaiTable.getParameter("term_tokaikyu_na"))%>" tabIndex="<%= tabIndex++ %>">
						</td>
					</tr>
				</table>
				<table class="term_sub" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<th nowrap>規格コード</th>
						<td nowrap>
							<input type="text" name="term_kikaku_cd" align="middle" maxlength="3" value="<%=MDWareHTMLUtility.toText(daitaiTable.getParameter("term_kikaku_cd"))%>" tabIndex="<%= tabIndex++ %>">
						</td>
						<th nowrap>規格名称</th>
						<td nowrap>
							<input type="text" name="term_kikaku_na" align="middle" maxlength="10" value="<%=MDWareHTMLUtility.toText(daitaiTable.getParameter("term_kikaku_na"))%>" tabIndex="<%= tabIndex++ %>">
						</td>
					</tr>
				</table>
				<table class="term_sub_msg_area" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<td align="center" nowrap>
							<font>コード、名称を入力した場合はあいまい検索となります。</font><br />
							<font>対象件数が多い場合、検索及び改ページに時間がかかることがあります。</font>
						</td>
					</tr>
				</table>
				<table class="term_sub_btn_area" border="0" cellspacing="0" cellpadding="3">
					<tr>
						<td align="center">
							<input type="button" name="search" value="&emsp;検&emsp;索&emsp;" onClick="daitaiSearch();" class="controlButton" tabIndex="<%=tabIndex++%>">
						</td>
					</tr>
				</table>
				<table class="term_sub_msg_area" border="0" cellspacing="0" cellpadding="2">
					<tr>
						<td align="center">
							<jsp:include page="ptl000002_InfoStringList.jsp" />
						</td>
					</tr>
				</table>
			</div>

			<!-- 遷移画面先指定 -->
			<input type="hidden" name="JobID"         value="">
			<input type="hidden" name="movePage"       value="">
			<!-- 次ページ指定 -->
			<input type=hidden name=move value="">

			<div class="list1_sub">
				<table align="center" style="border: 0px;">
					<tr>
						<td align="left">
							<table class="list1_sub" border="0" cellpadding="0" cellspacing="0">
								<tr>
									<th class="list1_sub_torihikisaki">取引先</th>
									<th class="list1_sub_santi">産地</th>
									<th class="list1_sub_tokaikyu">等級</th>
									<th class="list1_sub_kikaku">規格</th>
									<th class="list1_sub_gentanka">原単価</th>
									<th class="list1_sub_syukotanka">出庫単価</th>
									<th class="list1_sub_baitanka">売単価</th>
									<th class="list1_sub_hachu">発注数</th>
									<th class="list1_sub_sentaku"></th>
								</tr>
							</table>
							<%
								Iterator ite = daitaiList.getBeanIterator();
								String overF = "scroll";
								if( !ite.hasNext() ) overF = "hidden";
							%>
							<div style="overflow-y: <%=overF%>; HEIGHT: 256px;">
								<table class="list1_sub" border="0" cellpadding="0" cellspacing="0">
								<%
									popS05102_DaitaiSelBean popBean = new popS05102_DaitaiSelBean();
									while(ite.hasNext()){
										popBean = (popS05102_DaitaiSelBean)ite.next();
								%>
									<%
										String gentankaVl = "";
										String syukotankaVl = "";
										String baitankaVl = "";
										String hachuQt = "";
										String shimeFg = "";

										//値段関連項目が存在しない場合は空白
										if(!(popBean.getGentankaVl() == 0)){
											gentankaVl = popBean.getGentankaVlString();
										}
										if(!(popBean.getSyukotankaVl() == 0)){
											syukotankaVl = popBean.getSyukotankaVlString();
										}
										if(!(popBean.getBaitankaVl() == 0)){
											baitankaVl = popBean.getBaitankaVlString();
										}

										//shimeFg==1 の場合、選択項目を押下不可にする
										if(popBean.getShimeFg().equals("1")){
											shimeFg = "disabled=true";
										}
									%>
									<tr>
										<td class="list1_sub_torihikisaki" style="text-align:left;">
											&nbsp;<%=MDWareHTMLUtility.toLabel( popBean.getTorihikisakiCd() )%><br>&nbsp;<%=MDWareHTMLUtility.toLabel( popBean.getTorihikisakiNa() )%>
										</td>
										<td class="list1_sub_santi" style="text-align:left;">
											&nbsp;<%=MDWareHTMLUtility.toLabel( popBean.getSantiCd() )%><br>&nbsp;<%=MDWareHTMLUtility.toLabel( popBean.getSantiNa() )%>
										</td>
										<td class="list1_sub_tokaikyu" style="text-align:left;">
											&nbsp;<%=MDWareHTMLUtility.toLabel( popBean.getTokaikyuCd() )%><br>&nbsp;<%=MDWareHTMLUtility.toLabel( popBean.getTokaikyuNa() )%>
										</td>
										<td class="list1_sub_kikaku" style="text-align:left;">
											&nbsp;<%=MDWareHTMLUtility.toLabel( popBean.getKikakuCd() )%><br>&nbsp;<%=MDWareHTMLUtility.toLabel( popBean.getKikakuNa() )%>
										</td>
										<td class="list1_sub_gentanka" style="text-align:right;">
											<%=MDWareHTMLUtility.toLabel( gentankaVl )%>&nbsp;
										</td>
										<td class="list1_sub_syukotanka" style="text-align:right;">
											<%=MDWareHTMLUtility.toLabel( syukotankaVl )%>&nbsp;
										</td>
										<td class="list1_sub_baitanka" style="text-align:right;">
											<%=MDWareHTMLUtility.toLabel( baitankaVl )%>&nbsp;
										</td>
										<td class="list1_sub_hachu" style="text-align:right;">
											<%=MDWareHTMLUtility.toLabel( popBean.getHachuQtString() )%>&nbsp;
										</td>
										<td class="list1_sub_sentaku" align="center">
											<input type="button" name="select" value="選択" onClick="doCommit('<%=MDWareHTMLUtility.toLabel( popBean.getTorihikisakiCd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getTorihikisakiNa() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getSantiCd() )%>' , '<%=MDWareHTMLUtility.toLabel( popBean.getSantiNa() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getTokaikyuCd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getTokaikyuNa() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getKikakuCd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getKikakuNa() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getSyohinNa() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getHiKikakuNa() )%>');" tabIndex="<%= tabIndex++ %>" class="controlButton" <%=shimeFg%>>
										</td>
									</tr>
								<%
									}
								%>
								</table>
							</div>
						</td>
					</tr>
				</table>
			</div>

			<div class="list1_sub_pager">
				<table class="list1_sub_pager" align="center">
					<tr>
						<td align="center">
							<%
								String first = "";
								String prev = "";
								String next = "";
								String last = "";

								if ( daitaiList.getCurrentPageNumber() == 1)
								{
									first = "disabled";
									prev  = "disabled";
								}
								if ( daitaiList.getCurrentPageNumber() == daitaiList.getLastPageNumber() || (daitaiList.getLastPageNumber() == 0))
								{
									next = "disabled";
									last = "disabled";
								}
							%>
							<%if( first.length() > 0 ) {%>
								<input type="button" name="headPageButton" value="先　頭" onClick="" class="controlButton" disabled="true">
							<%} else {%>
								<input type="button" name="headPageButton" value="先　頭" onClick="changePage('first');" class="controlButton" tabIndex="<%= tabIndex++ %>">
							<%}%>
							<%if( prev.length() > 0 ) {%>
								<input type="button" name="prevPageButton" value="前　頁" onClick="" class="controlButton" disabled="true">
							<%} else {%>
								<input type="button" name="prevPageButton" value="前　頁" onClick="changePage('prev');" class="controlButton" tabIndex="<%= tabIndex++ %>">
							<%}%>
							<%if( next.length() > 0 ) {%>
								<input type="button" name="nextPageButton" value="次　頁" onClick="" class="controlButton" disabled="true">
							<%} else {%>
								<input type="button" name="nextPageButton" value="次　頁" onClick="changePage('next');" class="controlButton" tabIndex="<%= tabIndex++ %>">
							<%}%>
							<%if( last.length() > 0 ) {%>
								<input type="button" name="lastPageButton" value="最　終" onClick="" class="controlButton" disabled="true">
							<%} else {%>
								<input type="button" name="lastPageButton" value="最　終" onClick="changePage('last');" class="controlButton" tabIndex="<%= tabIndex++ %>">
							<%}%>
							<br />
							<span class="pagingInfo">( <%=daitaiList.getMaxRows()%>件中<%=daitaiList.getStartRowInPage()%>～<%=daitaiList.getEndRowInPage()%>件目 )</span>
						</td>
					</tr>
				</table>
			</div>

			<br>
			<table class="list1_sub_btn_area" >
			  <tr>
			    <td align="center">
			      <input  type="button" name="canceler" value="キャンセル" class="btn" onClick="javascript:window.close()" tabIndex="<%= tabIndex++ %>">
			    </td>
			  </tr>
			</table>
			<input type="hidden" name="ModifiedCondition" value="">
		</td>
	</tr>
</table>
</form>
</body>