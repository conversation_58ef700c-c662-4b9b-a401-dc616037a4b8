 <body bgcolor="#FFFFFF" text="#000000" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" onLoad="init();putOnLoadDisplay();">
<table width="100%" border="0" cellspacing="0" cellpadding="0" height="100%">
	<!------ システム共通ヘッダー  START ------>
	 <jsp:include page="ptl000001_Header.jsp?PARAM=緊急発注登録（商品店別）（SSN04008）"></jsp:include>
	<tr height="5"></tr>
	<!------ システム共通ヘッダー  END ------>

<!------ Body START ------>
<form name="MainForm" method="post" action="app">
<jsp:include page="rbs00000_common.jsp" />
  <!-- 原価チェック用 ---->
  <input type="hidden" name="GEN01_FUTOGO" value="<%= wkGenkaCheck.get("GEN01") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN01")).getFutogo() : "" %>">
  <input type="hidden" name="GEN01_VALUE"  value="<%= wkGenkaCheck.get("GEN01") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN01")).getValueString() : "" %>" >
  <input type="hidden" name="GEN01_MSG"    value="<%= wkGenkaCheck.get("GEN01") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN01")).getClMsg() : "" %>" >
  <input type="hidden" name="GEN02_FUTOGO" value="<%= wkGenkaCheck.get("GEN02") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN02")).getFutogo() : "" %>">
  <input type="hidden" name="GEN02_VALUE"  value="<%= wkGenkaCheck.get("GEN02") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN02")).getValueString() : "" %>" >
  <input type="hidden" name="GEN02_MSG"    value="<%= wkGenkaCheck.get("GEN02") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN02")).getClMsg() : "" %>" >
  <input type="hidden" name="GEN03_FUTOGO" value="<%= wkGenkaCheck.get("GEN03") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN03")).getFutogo() : "" %>">
  <input type="hidden" name="GEN03_VALUE"  value="<%= wkGenkaCheck.get("GEN03") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN03")).getValueString() : "" %>" >
  <input type="hidden" name="GEN03_MSG"    value="<%= wkGenkaCheck.get("GEN03") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN03")).getClMsg() : "" %>" >
  <input type="hidden" name="GEN04_FUTOGO" value="<%= wkGenkaCheck.get("GEN04") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN04")).getFutogo() : "" %>">
  <input type="hidden" name="GEN04_VALUE"  value="<%= wkGenkaCheck.get("GEN04") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN04")).getValueString() : "" %>" >
  <input type="hidden" name="GEN04_MSG"    value="<%= wkGenkaCheck.get("GEN04") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN04")).getClMsg() : "" %>" >

<input type="hidden" name="Modified" value="">
<input type="hidden" name="ModifiedCondition" value="">
<input type="hidden" name="deleteLine" />
<input type="hidden" name="displayMode" value="<%=emergencySyohinTorokuStatus.getDisplayMode()%>" />
<tr><td align="center" valign="top">
<% if(emergencySyohinTorokuStatus.getDisplayMode().equals("INSERT")) {
//登録ヘッダ
%>
	<TABLE class=kensaku cellSpacing=1 cellPadding=0 border=0 width="950">
		<input type="hidden" name="denp_nb" value="<%=status.getDenpNb()%>">
		<tr>

			<th nowrap>*取引先</th>
			<td nowrap align="left" style="width: 269px;">
	        <%
			//取引先権限振分
			if(RoleUtil.isTorihikisakiFurumai(role) || status.isTorihikisakiReadOnly()){
			%>
					<input type="text" name="torihikisaki_cd" value="<%= HTMLUtil.toText(status.getTorihikisakiCd()) %>" style="width: 50px;" size="8" maxlength="<%=torihikisakiCdLen %>" id="no_input_text" tabindex="-1"readOnly>
					<input type="text" name="torihikisaki_na" value="<%= HTMLUtil.toText(status.getTorihikisakiNa()) %>" style="width: 210px;" size="40" id="no_input_text" tabindex="-1" readOnly>
		    <%}else{%>
	                <input type="text" name="torihikisaki_cd" size="8" maxlength="<%=torihikisakiCdLen %>" value="<%= HTMLUtil.toText(status.getTorihikisakiCd()) %>" style="ime-mode:disabled; width: 52px;" />
	                <input type="text" name="torihikisaki_na"  value="<%= HTMLUtil.toText(status.getTorihikisakiNa()) %>" style="width: 110px;" id="no_input_text" tabindex="-1" readonly />
					<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_siireSel('MainForm.torihikisaki_cd','MainForm.torihikisaki_na');" />
					<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.torihikisaki_cd, MainForm.torihikisaki_na);" />
			<%}%>
			</td>

			<th nowrap>*部門</th>
			<td nowrap style="width: 239px;">
				<input type="text" name="bunrui1_cd" value="<%=status.getDptCd()%>" size="<%=status.DPT_CD_MAX_LENGTH%>" maxlength="<%=bunrui1CdLen %>" <%= status.isDptReadOnly() ? "id=\"no_input_text\" tabIndex=\"-1\" readOnly" : "" %> style="ime-mode:disabled; width: <%= status.DPT_CD_MAX_LENGTH*5 + 12 - (status.isDptReadOnly() ? 2 : 0) %>px;" />
				<input type="text" name="bunrui1_na" value="<%=status.getDptNa()%>" id="no_input_text" style="width: 110px;" tabindex="-1" readonly />
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_DptLineClass('MainForm.bunrui1_cd', 'MainForm.bunrui1_na','','','','',1,MainForm.bunrui1_cd.value,MainForm.bunrui1_na.value);"/>
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="javascript:clearCdNa('bunrui1')"/>
			</td>
			<th nowrap>*発注日</th>
			<td nowrap style="width: 183px;">
				<input type="text" name="hachu_dt" value="<%=status.getHachuDt()%>" <%=status.isHachuDtReadOnly() ? "id=\"no_input_text\" tabIndex=\"-1\" readOnly" : "" %> size="10" maxlength="8" style="ime-mode:disabled; width: <%= status.isHachuDtReadOnly() ? "60" : "62" %>px;" />
				<img src="./images/calendar.gif" width="18" height="18" align="absmiddle" alt="日付選択" onClick="callCalendar(MainForm, MainForm.hachu_dt);" />&nbsp;
				<small>（YYYYMMDD）</small>
			</td>
		</tr>
		<tr>
			<th nowrap>*商品</th>
			<td nowrap colspan="3">
				<input type="hidden" name="soba_kb" value="<%=status.getSobaKb().getCode()%>" />
				<input type="text" name="syohin_cd" value="<%=status.getSyohinCd()%>" size="18" maxlength="<%=syohinCdLen %>" <%= status.isSyohinReadOnly() ? "id=\"no_input_text\" tabIndex=\"-1\" readOnly style=\"width: 100px;" : "style=\"width: 102px;\"" %> onchange="changeSyohinCd()";  />
				<input type="text" name="syohin_na" value="<%= status.getSyohinNa() != null ? status.getSyohinNa() : "" %>" size="30" style="width: 160px;" maxlength="40" />
				<input type="text" name="hi_kikaku_na" value="<%= status.getHiKikakuNa() != null ? status.getHiKikakuNa() : "" %>" size="18" style="width: 100px;" id="no_input_text" tabindex="-1" readonly/>
					<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択"
						onClick="pop_syohinSel(
								'MainForm.syohin_cd',
								'MainForm.syohin_na',
								'MainForm.hi_kikaku_na',
								MainForm.bunrui1_cd,
								'',
								'',
								'1',
								'',
					    		document.MainForm.nohin_dt

					);"
				/>
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="javascript:clearCdNa('syohin')"/>
			</td>
			<th nowrap>*納品日</th>
			<td nowrap>
				<input type="text" name="nohin_dt" value="<%=status.getNohinDt()%>" <%=status.isNohinDtReadOnly() ? "id=\"no_input_text\" tabIndex=\"-1\" readOnly style=\"ime-mode:disabled; width: 60px;\"" : "style=\"ime-mode:disabled; width: 62px;\""%> size="10" maxlength="8" />
				<img src="./images/calendar.gif" width="18" height="18" align="absmiddle" alt="日付選択" onClick="callCalendar(MainForm, MainForm.nohin_dt);" />&nbsp;
				<small>（YYYYMMDD）</small>
			</td>
		</tr>
		<tr>
			<th nowrap>産地</th>
			<td nowrap>
				<input type="hidden" name="santi_cd" value="<%=status.getSantiCd()%>" />
				<input type="text"  name="santi_na" value="<%=status.getSantiNa()%>" id="no_input_text" style="width: 110px;" tabindex="-1" readonly />
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="if(checkTorihikisaki()) pop_santiSel('MainForm.santi_cd','MainForm.santi_na',MainForm.torihikisaki_cd.value)"/>
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="javascript:clearCdNa('santi')"/>
			</td>
			<th nowrap>等級</th>
			<td nowrap>
				<input type="hidden" name="tokaikyu_cd" value="<%=status.getTokaikyuCd()%>" />
				<input type="text" name="tokaikyu_na" value="<%=status.getTokaikyuNa()%>" style="width: 110px;" id="no_input_text" tabindex="-1" readonly />
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="if(checkTorihikisaki()) pop_tokaiqSel('MainForm.tokaikyu_cd','MainForm.tokaikyu_na',MainForm.torihikisaki_cd.value)"/>
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="javascript:clearCdNa('tokaikyu')"/>
			</td>
			<th nowrap>規格</th>
			<td nowrap>
				<input type="hidden" name="kikaku_cd" value="<%=status.getKikakuCd()%>" />
				<input type="text" name="kikaku_na" value="<%=status.getKikakuNa()%>" size="15" style="width: 85px;" id="no_input_text" tabindex="-1" readonly />
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="if(checkTorihikisaki()) pop_kikakuSel('MainForm.kikaku_cd','MainForm.kikaku_na',MainForm.torihikisaki_cd.value)"/>
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="javascript:clearCdNa('kikaku')"/>
			</td>
		</tr>
		<tr>
				<th nowrap>*納品区分</th>
				<td nowrap align="left">
				<select name="buturyu_kb" <%=status.isButuryuKbReadOnly() ? "disabled" : ""%> onChange="changeModified();">
					<option value=""></option>
<%
	if( maButuryuBh.getMaxRows() > 0 ) {
		for( Iterator ite = maButuryuBh.getBeanIterator(); ite.hasNext(); ) {
			MaButuryuBean bean = (MaButuryuBean)ite.next();
			if(!HTMLUtil.toText(bean.getShiwakeKb().trim()).equals("3")) { %>
				<% if (RoleUtil.isTorihikisakiFurumai(role)) {%>
					<% if (!bean.getButuryuKb().equals(ButuryuKb.TC_ICHIBA.getCode()) && !bean.getButuryuKb().equals(ButuryuKb.TYOKUNO_ICHIBA.getCode())){%>
				        <option value="<%= HTMLUtil.toText(bean.getButuryuKb()) %>"
				        <%= status.getButuryuKb().trim().equals(bean.getButuryuKb().trim()) ?  " selected" : "" %>>
				        <%= HTMLUtil.toLabel(bean.getButuryuNm()) %></option>
				        <%}	%>
				<%}	else {%>
					<option value="<%= HTMLUtil.toText(bean.getButuryuKb()) %>"
				    <%= status.getButuryuKb().trim().equals(bean.getButuryuKb().trim()) ?  " selected" : "" %>>
				    <%= HTMLUtil.toLabel(bean.getButuryuNm()) %></option>
				<%}	%>
<%			}	%>
<%		}	%>
<%	}	%>
				</select>
				</td>
				<th nowrap>経由センター</th>
				<td nowrap align="left">
				<select name="center_cd" <%=status.isCenterCdReadOnly() ? "disabled" : ""%> onChange="changeModified();">
					<option value=""></option>
<%
	if( maCenterBh.getMaxRows() > 0 ) {
		for( Iterator ite = maCenterBh.getBeanIterator(); ite.hasNext(); ) {
			MaCenterBean bean = (MaCenterBean)ite.next();  %>
			<option value="<%= HTMLUtil.toText(bean.getCenterCd()) %>"
		    <%= status.getCenterCd().trim().equals(bean.getCenterCd().trim()) ?  " selected" : "" %>>
		    <%= HTMLUtil.toLabel(bean.getCenterShortNa()) %></option>
<%		}	%>
<%	}	%>
				</select>
				</td>
				<th nowrap>*便</th>
				<td nowrap align="left">
					<input type="text" name="bin_kb" value="<%=status.getBinKb()%>" <%=status.isBinKbReadOnly() ? "id=\"no_input_text\" tabIndex=\"-1\" readOnly style=\"ime-mode:disabled; width: 15px;\"" : "style=\"ime-mode:disabled; width: 17px;\""%> size="1" maxlength="1"  value="1" onChange="changeModified()"/>
				</td>
		</tr>
      </TABLE>
<% } else { //参照ヘッダ %>
	<TABLE class=kensaku cellSpacing=1 cellPadding=0 border=0 width="950">
		<input type="hidden" name="denp_nb" value="<%=status.getDenpNb()%>">
		<tr>
			<th nowrap>取引先</th>
			<td nowrap align="left" style="width: 294px;">
				<input type="text" name="torihikisaki_cd" value="<%=status.getTorihikisakiCd()%>" style="width: 50px;"  size="8" maxlength="<%=torihikisakiCdLen %>" id="no_input_text" tabIndex="-1" readonly />
				<input type="text" name="torihikisaki_na" value="<%=status.getTorihikisakiNa()%>" style="width: 110px;"  size="20" id="no_input_text" tabIndex="-1" readOnly>
			</td>
			<th nowrap>部門</th>
			<td nowrap style="width: 244px;">
				<input type="text" name="bunrui1_cd" value="<%=status.getDptCd()%>" style="width: <%= status.DPT_CD_MAX_LENGTH*5 + 10 %>px;" size="<%=status.DPT_CD_MAX_LENGTH%>" maxlength="<%=bunrui1CdLen %>" id="no_input_text" tabIndex="-1" readonly />
				<input type="text" name="bunrui1_na" value="<%=status.getDptNa()%>" id="no_input_text" tabindex="-1" readonly style="width: 110px;" />
			</td>
			<th nowrap>発注日</th>
			<td nowrap style="width: 153px;">
				<input type="hidden" name="hachu_dt"         value="<%=status.getHachuDt()%>"  id="no_input_text" tabIndex="-1" readonly size="10" maxlength="8" />
				<input type="text"   name="hachu_dt_display" value="<%=HTMLUtil.toDate(status.getHachuDt(),"yyyy/MM/dd")%>"  id="no_input_text" tabIndex="-1" readonly size="13" style="width: 75px;" />
			</td>
		</tr>
		<tr>
			<th nowrap>商品</th>
			<td nowrap colspan="3">
				<input type="hidden" name="soba_kb" value="<%=status.getSobaKb().getCode()%>" />
				<input type="text" name="syohin_cd" value="<%=status.getSyohinCd()%>" size="18" style="width: 100px;" maxlength="<%=syohinCdLen %>" id="no_input_text" tabIndex="-1" readonly />
				<input type="text" name="syohin_na" value="<%= status.getSyohinNa() != null ? status.getSyohinNa() : "" %>" size="30" maxlength="40" id="no_input_text" tabindex="-1" style="ime-mode:disabled; width: 160px;" readonly/>
				<input type="text" name="hi_kikaku_na" value="<%= status.getHiKikakuNa() != null ? status.getHiKikakuNa() : "" %>" size="18" style="width: 100px;" id="no_input_text" tabindex="-1" readonly/>
			</td>
			<th nowrap>納品日</th>
			<td nowrap>
				<input type="hidden" name="nohin_dt"         value="<%=status.getNohinDt()%>"  id="no_input_text" tabIndex="-1" readonly size="10" maxlength="8" />
				<input type="text"   name="nohin_dt_display" value="<%=HTMLUtil.toDate(status.getNohinDt(),"yyyy/MM/dd")%>"  id="no_input_text" tabIndex="-1" readonly size="13" style="width: 75px;" />

			</td>
		</tr>
		<tr>
		<th nowrap>産地</th>
		<td nowrap>
			<input type="hidden" name="santi_cd" value="<%=status.getSantiCd()%>" />
			<input type="text"  name="santi_na" value="<%=status.getSantiNa()%>" style="width: 110px;" id="no_input_text" tabindex="-1" readonly />
		</td>
		<th nowrap>等級</th>
		<td nowrap>
			<input type="hidden" name="tokaikyu_cd" value="<%=status.getTokaikyuCd()%>" />
			<input type="text" name="tokaikyu_na" value="<%=status.getTokaikyuNa()%>" style="width: 110px;" id="no_input_text" tabindex="-1" readonly />
		</td>
		<th nowrap>規格</th>
		<td nowrap>
			<input type="hidden" name="kikaku_cd" value="<%=status.getKikakuCd()%>" />
			<input type="text" name="kikaku_na" value="<%=status.getKikakuNa()%>" size="15" style="width: 85px;" id="no_input_text" tabindex="-1" readonly />
		</td>
		</tr>
		<tr>
			<th nowrap>納品区分</th>
	        <td nowrap align="left">
				<select name="buturyu_kb_disp" disabled onChange="changeModified();">
				<option value=""></option>
<%
	if( maButuryuBh.getMaxRows() > 0 ) {
		for( Iterator ite = maButuryuBh.getBeanIterator(); ite.hasNext(); ) {
		MaButuryuBean bean = (MaButuryuBean)ite.next();
			if(!HTMLUtil.toText(bean.getShiwakeKb().trim()).equals("3")) {%>
				<option value="<%= HTMLUtil.toText(bean.getButuryuKb()) %>"
				<%= status.getButuryuKb().trim().equals(bean.getButuryuKb().trim()) ?  " selected" : "" %>>
				<%= HTMLUtil.toLabel(bean.getButuryuNm()) %></option>
<%			}	%>
<%		}	%>
<%	}	%>
				</select>
				<input type="text" name="buturyu_kb" value="<%=status.getButuryuKb()%>" style="display: none" readonly />
			</td>
			<th nowrap>経由センター</th>
			<td nowrap align="left">
				<select name="center_cd" <%=status.isCenterCdReadOnly() ? "disabled" : ""%> onChange="changeModified();">
				<option value=""></option>
<%
	if( maCenterBh.getMaxRows() > 0 ) {
		for( Iterator ite = maCenterBh.getBeanIterator(); ite.hasNext(); ) {
			MaCenterBean bean = (MaCenterBean)ite.next();  %>
			<option value="<%= HTMLUtil.toText(bean.getCenterCd()) %>"
		    <%= status.getCenterCd().trim().equals(bean.getCenterCd().trim()) ?  " selected" : "" %>>
		    <%= HTMLUtil.toLabel(bean.getCenterShortNa()) %></option>
<%		}	%>
<%	}	%>
				</select>
				<input type="text" name="center_cd" value="<%=status.getCenterCd()%>" style="display: none" readonly />
			</td>
			<th nowrap>便</th>
			<td nowrap align="left">
				<input type="text" name="bin_kb" value="<%=status.getBinKb()%>" id="no_input_text" tabIndex="-1" readonly size="1" style="width: 15px;" maxlength="1" value="1" onChange="changeModified()"/>
			</td>
		</tr>
</TABLE>
<% } %>
<br>
<jsp:include page="InfoStringMdWare.jsp" />
<br>


	<table width="750" border="0" cellspacing="0" cellpadding="0" >
		<tr>
			<td align="center">
				<input type="button" value="店舗表示"  name="" class="btn" style="width: 77px;" onClick="doSearchTran();" <% if(!emergencySyohinTorokuStatus.getDisplayMode().equals("INSERT")) { %>disabled<% } %>>
			</td>
	</table>
<br>
		<!--- 商品情報 --->
		<table border="0" cellspacing="1" cellpadding="0" class="shori_info" style="line-height: 22px;">
			<tr>
				<th nowrap>原単価</th>
				<td nowrap width="100" align="left">
					<input type="text" name="gentanka_vl" size="12" maxlength="10" value="<%= status.getGentankaVl() != 0 ?  HTMLUtil.toText(Double.toString(status.getGentankaVl()), "#0.00") : "" %>" <%=status.isGentankaReadOnly() ? "id=\"no_input_text\" style=\"ime-mode:disabled; width: 70px;\" tabIndex=\"-1\" readOnly" : "style=\"ime-mode:disabled; width: 72px;\""%> class="numeric" onChange="changeModified()" />
				</td>
				<th nowrap>売単価</th>
				<td nowrap width="100" align="left">
					<input type="text" name="baitanka_vl" size="8" maxlength="7" value="<%= status.getBaitankaVl() != 0 ?  HTMLUtil.toText(Long.toString(status.getBaitankaVl()), "#0" ) : "" %>" maxlength="8" <%=status.isBaitankaReadOnly() ? "id=\"no_input_text\" tabIndex=\"-1\" readOnly style=\"ime-mode:disabled; width: 50px;\"" : "style=\"ime-mode:disabled; width: 52px;\""%> class="numeric" onChange="changeModified()" />
				</td>
				<th nowrap>発注単位</th>
				<td id="id_hachu_tani_cd">
					<select align="left" name="hachu_tani_cd" value="<%=HTMLUtil.toText(status.getHachuTaniCd())%>" size="<%=status.HACHU_TANI_CD_MAX_LENGTH%>"  <%=status.isHachuTaniCdReadOnly() ? "disabled" : ""%> style="width: 60px;" onChange="changeModified()">
						<option value=""></option>
					<%
					   if( nohinTaniList != null && nohinTaniList.getBeanList() != null && nohinTaniList.getBeanList().size() > 0) {
						List selectList = nohinTaniList.getBeanList();
						  for(int k = 0;k < selectList.size(); k++ ) {
						  	MaHachuTaniBean bean = (MaHachuTaniBean)selectList.get(k);
						  	String hachuTaniCd = bean.getHachuTaniCd();
					%>
							<option value="<%=hachuTaniCd%>"<%=HTMLUtil.toText(status.getHachuTaniCd()).trim().equals(hachuTaniCd.trim()) ? "selected" : ""%>><%=bean.getHachuTaniNa()%>
							</option>
					<%
						  }
					   }
					%>
					</select>
					<input type="hidden" name="chk_hachu_tani_cd" value="<%=HTMLUtil.toText(status.getHachuTaniCd()).trim()%>">
				</td>
			</tr>
			<tr>
				<th nowrap>入数</th>
				<td nowrap width="100" align="left">
					<input type="text" name="irisu_qt" size="4" maxlength="4" value="<%= status.getIrisuQt() != 0 ?  HTMLUtil.toText(status.getIrisuQt(), "0") : "" %>" + <%=status.isIrisuQtReadOnly() ? "id=\"no_input_text\" tabIndex=\"-1\" readOnly style=\"ime-mode:disabled; width: 30px;\"" : "style=\"ime-mode:disabled; width: 32px;\""%> maxlength="4" class="numeric" onchange="changeModified()"/>
				</td>
				<th nowrap>定貫区分</th>
				<td nowrap height="22" class="string_label">
					<input type="hidden" name="teikan_kb" value="<%=status.getTeikanKb().getCode()%>"/><%= ((status.getTeikanKb().toString())=="UNKNOWN" )?"":status.getTeikanKb().toString() %>
				</td>
			</tr>
		</table>

	<br />

	<!--- 一括反映 --->
	<table width="900px" border="0" cellspacing="1" cellpadding="0" class="kensaku">
		<tr>
			<th nowrap>納品数</th>
 			<td nowrap align="left">
 				<input class="numeric" maxlength="4" size="5" type="text" name="hachuQtJs"  value="" id="<%= noInputStr %>" <%= disabledStr %> style="width: <%= noInputStr == "" ? "37px;" : "35px;" %>" />
				<input type="button" value="&nbsp;&nbsp;反映&nbsp;&nbsp;" onClick="setNohinQtJs(document.MainForm.hachuQtJs);" style="width: 62px; height: 21px;" <%= buttonDis %> >
			</td>

			<th nowrap>納品数量</th>
 			<td nowrap align="left">
 				<input class="numeric" maxlength="9" size="11" type="text" name="hachuSuryoQtJs"  value="" id="<%= noInputStr %>" <%= disabledStr %> style="width: <%= disabledStr == "" ? "67px;" : "65px;" %>" />
				<input type="button" value="&nbsp;&nbsp;反映&nbsp;&nbsp;" onClick="setNohinSuryoQtJs(document.MainForm.hachuSuryoQtJs);" style="width: 62px; height: 21px;" <%= buttonDis %> >
			</td>
<%
		if(!status.isIkkatuSiireDenpFg()){
%>
			<th nowrap>原単価</th>
			<td nowrap align="left">
				<input class="numeric" maxlength="10" size="12" type="text" name="gentankaVlJs"  value="" id="<%= noInputStr %>" <%= disabledStr %> style="width: <%= disabledStr == "" ? "72px;" : "70px;" %>" />
				<input type="button" value="&nbsp;&nbsp;反映&nbsp;&nbsp;" onClick="setGentankaVlJs(document.MainForm.gentankaVlJs)" style="width: 62px; height: 21px;" <%= buttonDis %> >
			</td>
<%
		} else {
%>
			<%	if (!RoleUtil.isTorihikisakiFurumai(role)) {	%>
			        <th nowrap>出庫単価</th>
			        <td nowrap align="left">
				    <input class="numeric" maxlength="10" size="12" type="text" name="syukkotankaVIJs" value="" id="<%= noInputStr %>" <%= disabledStr %> style="width: <%= disabledStr == "" ? "72px;" : "70px;" %>" />
				    <input type="button" value="&nbsp;&nbsp;反映&nbsp;&nbsp;" onClick="setSyukkotankaVlJs(document.MainForm.syukkotankaVIJs)" style="width: 62px; height: 21px;" <%= buttonDis %> >
			        </td>
			<%	} else {  %>
			        <th nowrap>出庫単価</th>
			        <td nowrap align="left">
				    <input class="numeric" maxlength="10" size="12" style="width: 70px;" type="text" name="syukkotankaVIJs" value="" id="no_input_text" readOnly tabindex="-1" />
				    <input type="button" value="&nbsp;&nbsp;反映&nbsp;&nbsp;" onClick="setSyukkotankaVlJs(document.MainForm.syukkotankaVIJs)" style="width: 62px; height: 21px;" disabled >
			        </td>
			<%	}  %>
<%
		}
%>
			<th nowrap>売単価</th>
			<td nowrap align="left">
				<input class="numeric" maxlength="7" size="12" type="text" name="baitankaVlJs" value="" id="<%= noInputStr %>" <%= disabledStr %> style="width: <%= disabledStr == "" ? "72px;" : "70px;" %>" />
				<input type="button" value="&nbsp;&nbsp;反映&nbsp;&nbsp;" onClick="setBaitankaVlJs(document.MainForm.baitankaVlJs)" style="width: 62px; height: 21px;" <%= buttonDis %> >
			</td>
		</tr>
	</table>

<CENTER>
		<table width="900" border="0" cellspacing="0" cellpadding="2" class="guide">
<tr><td align="center">


</td></tr>
</table>
</CENTER>

	<br />

<table>
<tr align = "left">
<td>
	<div align="left" style="overflow-x:hidden; width: 980; height:30px;">
	<table class="data" cellSpacing="1" cellPadding="0" border="0" height="30" >
	    <tr>
			<th nowrap width="74" align="center">店舗コード</th>
			<th nowrap width="250" align="center">店舗名</th>
			<th nowrap width="90"  align="center">納品数</th>
			<th nowrap width="105" align="center">納品数量</th>
			<th nowrap width="120" align="center">原単価</th>
			<%// 取引先は非表示
			if (RoleUtil.isTorihikisakiFurumai(role)) { %>
				<th nowrap width="120" align="center"></th>
			<%} else { %>
				<th nowrap width="120" align="center">出庫単価</th>
			<%} %>
			<th nowrap width="120" align="center">売単価</th>
			<th nowrap width="15" ></th>
	    </tr>
	</table>
	</div>

	<div  align="left" style="overflow:scroll;overflow-x:hidden; width: 980; height: 185px;">
	<table border="0" cellspacing="1" cellpadding="0" class="data"  style="line-height: 22px;">

<%for ( int i=0;i<syohinTorokMeisaiList.size();i++ ) {
	String style = ((i%2)==0)?"style=\"background-color:#FFFFCF\"":"";
	String style_color = ((i%2)==0)?"background-color:#FFFFCF":"";

	EmergencySyohinTorokuMeisai hachuBean = (EmergencySyohinTorokuMeisai)syohinTorokMeisaiList.get(i);
%>
		<tr>
			<!------ 店舗コード ------>
			<td nowrap width="74" <%=style%> align="center" >
				<input type="hidden" name="tenpo_cd_<%=i%>" value="<%=hachuBean.getTenpoCd()%>"/>
				<%=hachuBean.getTenpoCd()%>
                <!------ 変更フラグ ------>
                <input type="hidden" name="change_fg_<%=i%>" value="<%=hachuBean.getChangeFg()%>" />
			</td>

			<!------ 店舗名 ------>
			<td nowrap width="250" align="center" <%=style%>>
				<INPUT type="text" readOnly size=43 style="border-width: 0px; width: 227px; <%=style_color%> ; " value="<%=hachuBean.getTenpoNa()%>" tabIndex="-1" >
			</td>

			<!------ 納品数 ------>
			<td nowrap width="90"  align="center" <%=style%>>
				<input type="text" name="nohin_qt_<%=i%>" maxlength="4" size="6" value="<%=hachuBean.getNohinQt() == 0 ? "" : HTMLUtil.toText(hachuBean.getNohinQt())%>" class="numeric" onKeyDown="setMeisaiColFocus('nohin_qt',<%=i%>);" onblur="nohinQtCheck(this, 3);" onChange="changeModifiedCondition();changeLine('<%=i%>');" <%=hachuBean.isNohinQtReadOnly() ? "readonly tabIndex=\"-1\" id=\"no_input_text\"  style=\"ime-mode:disabled; width: 40px;\"" : "style=\"ime-mode:disabled; width: 42px;\"" %>>
			</td>

			<!------ 納品数量 ------>
			<td nowrap width="105"  align="center" <%=style%>>
				<input type="text" name="nohin_suryo_qt_<%=i%>" maxlength="9" size="11" value="<%=hachuBean.getNohinSuryoQt() == 0 ? "" : HTMLUtil.toText(hachuBean.getNohinSuryoQt(), "0.##")%>"  class="numeric" onKeyDown="setMeisaiColFocus('nohin_suryo_qt',<%=i%>);" onblur="nohinSuryoCheck(this, 2);" onChange="changeModifiedCondition();changeLine('<%=i%>');" <%=hachuBean.isNohinSuryoQtReadOnly() ? "readonly tabIndex=\"-1\" id=\"no_input_text\" style=\"ime-mode:disabled; width: 65px;\"":"style=\"ime-mode:disabled; width: 67px;\""%>>
			</td>

			<!------ 原単価 ------>
			<td nowrap width="120"  align="center" <%=style%>>
                <input type="text" name="gentanka_vl_<%=i%>" maxlength="10" size="12" value="<%= hachuBean.getGentankaVl() == 0 || status.isIkkatuSiireDenpFg()  ? "" : HTMLUtil.toText(hachuBean.getGentankaVl(), "0.00")  %>" class="numeric" onKeyDown="setMeisaiColFocus('gentanka_vl',<%=i%>);" onChange="changeModifiedCondition();changeLine('<%=i%>');" <%=hachuBean.isGentankaReadOnly() ? "readonly tabIndex=\"-1\" id=\"no_input_text\"  style=\"ime-mode:disabled; width: 70px;\"":"style=\"ime-mode:disabled; width: 72px;\""%>/>
			</td>

			<!------ 出庫単価 ------>
			<td nowrap width="120"  align="center" <%=style%>>
					<%
					// 取引先は非表示
					if(RoleUtil.isTorihikisakiFurumai(role)){
					%>
					    <%	if (status.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
					    		|| status.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
					    		|| status.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
					    		|| status.getButuryuKb().equals(ButuryuKb.RS.getCode())) {	%>
								<input type="hidden" name="syukkotanka_vl_<%=i%>" value="<%= hachuBean.getSyukkotankaVl() != 0 ?  HTMLUtil.toText(hachuBean.getSyukkotankaVl(), "0.00") : "" %>" tabindex="-1" disabled readonly>
					    <%	} else { %>
					    		<input type="hidden" name="syukkotanka_vl_<%=i%>" value="<%= hachuBean.getSyukkotankaVl() != 0 ?  HTMLUtil.toText(hachuBean.getSyukkotankaVl(), "0.00") : "" %>" tabindex="-1" id=\"no_input_text\">
					    <%	}	%>
			    	<%}else{%>
                		<input type="text"   name="syukkotanka_vl_<%=i%>" maxlength="10" size="12" value="<%= hachuBean.getSyukkotankaVl() != 0 ?  HTMLUtil.toText(hachuBean.getSyukkotankaVl(), "0.00") : "" %>" class="numeric" onKeyDown="setMeisaiColFocus('syukkotanka_vl',<%=i%>);" onChange="changeModifiedCondition();changeLine('<%=i%>');" <%=hachuBean.isSyukkotankaReadOnly() ? "readonly tabIndex=\"-1\" id=\"no_input_text\" style=\"ime-mode:disabled; width: 70px;\"":"style=\"ime-mode:disabled; width: 72px;\""%>/>
					<%}%>
			</td>

			<!------ 売単価 ------>
			<td nowrap width="120"  align="center" <%=style%>>
				<input type="text" name="baitanka_vl_<%=i%>" maxlength="7" size="10" value="<%= ( emergencySyohinTorokuStatus.getDisplayMode().equals("INSERT") || hachuBean.getNohinSuryoQt() == 0 ) && hachuBean.getBaitankaVl() == 0  ? "" : HTMLUtil.toText(hachuBean.getBaitankaVl(), "0") %>" class="numeric" onKeyDown="setMeisaiColFocus('baitanka_vl',<%=i%>);" onChange="changeModifiedCondition();changeLine('<%=i%>');" <%=hachuBean.isBaitankaReadOnly()?"readonly tabIndex=\"-1\" id=\"no_input_text\"  style=\"ime-mode:disabled; width: 60px;\"":"style=\"ime-mode:disabled; width: 62px;\""%>/>
			</td>
		</tr>
<%}%>
	</table>
	</div>

</td>
</tr>
</table>

	<br>
	<table width="500" border="0" cellspacing="1" cellpadding="0" class="data" style="line-height: 22px;">
		<tr>
			<th nowrap >納品数合計</th>
			<td width="150" align="center">
				<input type="text" name="hachu_kei_qt" size="15" style="width: 85px;" maxlength="15" value="100" class="numeric" id="no_input_text" tabindex="-1" readonly >
			</td>
			<th nowrap >納品数量合計</th>
			<td width="150" align="center">
				<input type="text" name="hachu_suryo_kei_qt" size="15" style="width: 85px;" maxlength="15" value="100" class="numeric" id="no_input_text" tabindex="-1" readonly >
			</td>
		</tr>
	</table>

	<br>
	<table width="750" border="0" cellspacing="0" cellpadding="0" >
		<tr>
			<td align="left">
				<input type="button" value="緊急発注登録一覧（商品店別）" class="btn" onClick="doBackTran();" style="width: 254px;" />
			</td>
			<td align="right">
<%if(status.getDisplayMode().equals("REFERENCE")){%>
				<%	if (RoleUtil.isTorihikisakiFurumai(role)) {	%>
				    <%	if (status.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
				    		|| status.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
				    		|| status.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
				    		|| status.getButuryuKb().equals(ButuryuKb.RS.getCode())) {	%>
				    		    <input type="button" value="&nbsp;コピー&nbsp;"  name="" class="btn"  onClick="doCopyTran();" style="width: 56px;">
						        <input type="button" value="全クリア" name="" class="btn"  onClick="doAllCrearTran();" <%=buttonDis %>  style="width: 59px;">
				    <%	}	%>
				<%	} else {	%>
				        <input type="button" value="&nbsp;コピー&nbsp;"  name="" class="btn"  onClick="doCopyTran();"  style="width: 56px;">
						<input type="button" value="全クリア" name="" class="btn"  onClick="doAllCrearTran();" <%=buttonDis %> style="width: 59px;">
				<%	} %>
<%}else if(status.getDisplayMode().equals("UPDATE")){%>
				<input type="button" value="&nbsp;更&emsp;新&nbsp;"  name="" class="btn"  onClick="doUpdateTran();" style="width: 70px;">
				<%	if (RoleUtil.isTorihikisakiFurumai(role)) {	%>
				    <%	if (status.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
				    		|| status.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
				    		|| status.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
				    		|| status.getButuryuKb().equals(ButuryuKb.RS.getCode())) {	%>
				    		    <input type="button" value="&nbsp;コピー&nbsp;"  name="" class="btn"  onClick="doCopyTran();"  style="width: 56px;">
						        <input type="button" value="全クリア" name="" class="btn"  onClick="doAllCrearTran();" <%=buttonDis %>  style="width: 59px;">
				    <%	}	%>
				<%	} else {	%>
				        <input type="button" value="&nbsp;コピー&nbsp;"  name="" class="btn"  onClick="doCopyTran();"  style="width: 56px;">
						<input type="button" value="全クリア" name="" class="btn"  onClick="doAllCrearTran();" <%=buttonDis %>  style="width: 59px;">
				<%	} %>
<%} else {%>
				<input type="button" value="&nbsp;登&emsp;録&nbsp;"  name="" class="btn"  onClick="doInsertTran();" style="width: 70px;">
				<%	if (RoleUtil.isTorihikisakiFurumai(role)) {	%>
				    <%	if (status.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
				    		|| status.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
				    		|| status.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
				    		|| status.getButuryuKb().equals(ButuryuKb.RS.getCode())
				    		|| status.getButuryuKb().equals("")) {	%>
						        <input type="button" value="全クリア" name="" class="btn"  onClick="doAllCrearTran();" <%=buttonDis %> style="width: 59px;">
				    <%	}	%>
				<%	} else {	%>
						<input type="button" value="全クリア" name="" class="btn"  onClick="doAllCrearTran();" <%=buttonDis %> style="width: 59px;">
				<%	} %>
<%}%>
			</td>
	 </table>


</form>
</div>

<!-- Body END -->
<!---- システム共通フッター START ---->
<!---- システム共通フッター END ---->
</table>
</body>