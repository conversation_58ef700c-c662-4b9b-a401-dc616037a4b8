<body bgcolor="#FFFFFF" text="#000000" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" onload="setFocus();init();putOnLoadDisplay();outputList();">
	<form name="MainForm" method="post" action="app">
		<table cellspacing="0" cellpadding="0" border="0" width="100%">
			<tr>
				<td>
					<jsp:include page="ptl000001_Header.jsp?PARAM=納品一括処理（SSN04010）"></jsp:include>
				</td>
			</tr>
			<tr>
				<td align="center">
					<jsp:include page="rbs00000_common.jsp" flush="true" />
					<input type="hidden" name="Modified" value="">
					<input type="hidden" name="ModifiedCondition" value="">
					<input type="hidden" name="outPutFlg" value="<%= outPutFlg %>" />

					<!------ 検索条件部 START ------------------------------------------------------------------------------------------------------------------------------------------------------->
					<div class="term">
						<!-- 取引先 -->
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>*取引先</th>
								<td colspan="3">
									<%
										//取引先権限振分
									if (RoleUtil.isTorihikisakiFurumai(role)) {
									%>
									<input type="text" name="term_torihikisaki_cd" value="<%=HTMLUtil.toText(nohinAllStatus.getTorihikisakiCd())%>" tabindex="-1" readOnly>
									<input type="text" name="term_torihikisaki_na" value="<%=HTMLUtil.toText(nohinAllStatus.getTorihikisakiNa())%>" tabindex="-1" readOnly>
									<%
										} else {
									%>
									<input type="text" name="term_torihikisaki_cd" value="<%=HTMLUtil.toText(nohinAllStatus.getTorihikisakiCd())%>" maxlength="<%=torihikisakiCdLen%>" />
									<input type="text" name="term_torihikisaki_na" value="<%=HTMLUtil.toText(nohinAllStatus.getTorihikisakiNa())%>" tabindex="-1" readonly />
									<input type="button" value="選択" class="detailButton" onClick="pop_siireSel('MainForm.term_torihikisaki_cd','MainForm.term_torihikisaki_na');" />
									<input type="button" value="クリア" class="detailButton clearButton" onClick="clear2(MainForm.term_torihikisaki_cd, MainForm.term_torihikisaki_na);" />
									<%
										}
									%>
								</td>
							</tr>
						</table>
						<!-- 納品日 -->
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>*納品日</th>
								<td colspan="3">
									<input type="text" name="term_nohin_dt_from" value="<%=HTMLUtil.toText(nohinAllStatus.getNohinDtFrom())%>" maxlength="8" />
									<input type="button" value="日付選択" class="detailButton" onClick="callCalendar(MainForm, MainForm.term_nohin_dt_from);" />(自)&nbsp;～
									<input type="text" name="term_nohin_dt_to" value="<%=HTMLUtil.toText(nohinAllStatus.getNohinDtTo())%>" maxlength="8" />
									<input type="button" value="日付選択" class="detailButton" onClick="callCalendar(MainForm, MainForm.term_nohin_dt_to);" />(至)
									<small>（YYYYMMDD）</small>
								</td>
							</tr>
						</table>
						<!-- 部門 -->
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>*部門</th>
								<td colspan="3">
									<input type="text" name="term_bunrui1_cd" value="<%=HTMLUtil.toText(nohinAllStatus.getBunrui1Cd())%>" maxlength="<%=bunrui1CdLen%>" />
									<input type="text" name="term_bunrui1_na" value="<%=HTMLUtil.toText(nohinAllStatus.getBunrui1Na())%>" tabindex="-1" readonly />
									<input type="button" value="選択" class="detailButton" onClick="pop_DptLineClass('MainForm.term_bunrui1_cd','MainForm.term_bunrui1_na','MainForm.term_bunrui2_cd','MainForm.term_bunrui2_na','MainForm.term_bunrui5_cd','MainForm.term_bunrui5_na','1',MainForm.term_bunrui1_cd.value , '');" />
									<input type="button" value="クリア" class="detailButton clearButton" onClick="clear2(MainForm.term_bunrui1_cd, MainForm.term_bunrui1_na);" />
								</td>
							</tr>
						</table>
						<!-- 便 -->
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>便</th>
								<td>
									<input type="text" name="term_bin_nm" value="<%=HTMLUtil.toText(nohinAllStatus.getBinNm())%>" maxlength="1" />
								</td>
							</tr>
						</table>
						<!-- 店舗 -->
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>店舗</th>
								<td colspan="3">
									<input type="text" name="term_tenpo_cd" value="<%=HTMLUtil.toText(nohinAllStatus.getTenpoCd())%>" maxlength="<%=tenpoCdLen%>" />
									<input type="text" name="term_tenpo_na" value="<%=HTMLUtil.toText(nohinAllStatus.getTenpoNa())%>" tabindex="-1" readonly />
									<input type="button" value="選択" class="detailButton" onClick="pop_tenpoSel('MainForm.term_tenpo_cd','MainForm.term_tenpo_na','');" />
									<input type="button" value="クリア" class="detailButton clearButton" onClick="clear2(MainForm.term_tenpo_cd, MainForm.term_tenpo_na);" />
								</td>
							</tr>
						</table>
						<!-- 納品区分 -->
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>納品区分</th>
								<td>
									<select name="term_buturyu_kb" onChange="changeModifiedCondition();">
										<option value=""></option>
										<%
										if (maButuryuBh.getMaxRows() > 0) {
											for (Iterator ite = maButuryuBh.getBeanIterator(); ite.hasNext();) {
												MaButuryuBean bean = (MaButuryuBean) ite.next();
										%>
										<option value="<%=HTMLUtil.toText(bean.getButuryuKb().trim())%>" <%=nohinAllStatus.getButuryuKb().trim().equals(bean.getButuryuKb().trim()) ? " selected" : ""%>><%=HTMLUtil.toLabel(bean.getButuryuNm())%></option>
										<%
											}
										}
										%>
									</select>
								</td>
							</tr>
						</table>
						<!-- 対象データ -->
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>*対象データ</th>
								<td colspan="3">
									<label><input type="checkbox" name="term_data_soba" value="" <%=nohinAllStatus.isDataSoba() ? "checked" : ""%> onClick="changeModifiedCondition();" /><span>相場</span></label>&emsp;&emsp;
									<label><input type="checkbox" name="term_data_hisoba_tokubai" value="" <%=nohinAllStatus.isDataHisobaTokubai() ? "checked" : ""%> onClick="changeModifiedCondition();" /><span>非相場/特売</span></label>&emsp;&emsp;
									<label><input type="checkbox" name="term_data_kinkyu" value="" <%=nohinAllStatus.isDataKinkyu() ? "checked" : ""%> onClick="changeModifiedCondition();" /><span>緊急</span></label>&emsp;&emsp;
								</td>
							</tr>
						</table>
						<!-- 経由センタ -->
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>経由センタ</th>
								<td colspan="3">
									<%
										//センター権限振分
									if (RoleUtil.isCenterFurumai(role)) {
									%>
									<input type="text" name="term_center_cd" value="<%=HTMLUtil.toText(nohinAllStatus.getCenterCd())%>" tabindex="-1" readOnly>
									<input type="text" name="term_center_na" value="<%=HTMLUtil.toText(nohinAllStatus.getCenterNa())%>" tabindex="-1" readonly />
									<%
										} else {
									%>
									<input type="text" name="term_center_cd" value="<%=HTMLUtil.toText(nohinAllStatus.getCenterCd())%>" maxlength="<%=centerCdLen%>" />
									<input type="text" name="term_center_na" value="<%=HTMLUtil.toText(nohinAllStatus.getCenterNa())%>" tabindex="-1" readonly />
									<input type="button" value="選択" class="detailButton" onClick="pop_centerSel('MainForm.term_center_cd','MainForm.term_center_na');" />
									<input type="button" value="クリア" class="detailButton clearButton" onClick="clear2(MainForm.term_center_cd, MainForm.term_center_na);" />
									<%
										}
									%>
								</td>
							</tr>
						</table>
					</div>
					<div class="term">
						<table class="term_guide">
							<tr>
								<td>
									<font color='#0000FF'>※必要のない対象データはチェックをはずしてください</font>
								</td>
							</tr>
						</table>
					</div>

					<br />
					<div class="term">
						<table class="term_btn_area">
							<tr>
								<td>
									<center>
										<input type="button" value="検索" class="controlButton" onClick="searchCommand();" />
										<input type="button" value="戻る" class="controlButton" onClick="isModifiedTran('jutyuSubMenu');" />
									</center>
								</td>
							</tr>
						</table>
					</div>

					<br />
					<!------ 検索条件部 END   ------------------------------------------------------------------------------------------------------------------------------------------------------->
					<!------ メッセージ表示部 START ------------------------------------------------------------------------------------------------------------------------------------------------->
					<div id="messageBase" class="term">
						<table class="term_msg_area" cellpadding="0" cellspacing="0" border="0" align="center">
							<tr>
								<td align="center">
									<jsp:include page="InfoStringMdWare.jsp" />
								</td>
							</tr>
						</table>
					</div>

					<br />
					<!------ メッセージ表示部 END    ------------------------------------------------------------------------------------------------------------------------------------------------>
					<!------ 検索結果  START  ------------------------------------------------------------------------------------------------------------------------------------------------------->
					<%
					if (nohinAllStatus.hasSearchResult()) {
					%>
					<div class="list1" align="left">
						<table class="list1_search" cellpadding="0" cellspacing="0">
							<tr>
								<th class="list1_taisyo_data" align="center">対象データ</th>
								<th class="list1_nohin" align="center">納品</th>
								<th class="list1_nohin_dt" align="center">納品日</th>
								<th class="list1_denpyo_qt" align="center">伝票枚数</th>
								<th class="list1_genka_kei" align="center">原価金額<br>合計</th>
								<th class="list1_baika_kei" align="center">売価金額<br>合計</th>
							</tr>
						</table>

						<div id="result_scroll" align="left">
							<table border="0" cellspacing="0" cellpadding="0" class="list1_search">
								<%
									// 明細表示
								Iterator itr = nohinAllStatus.getBeanList().iterator();
								while (itr.hasNext()) {
									NohinAllBean bean = (NohinAllBean) itr.next();
								%>
								<tr>
									<td class="list1_taisyo_data" align="center"><%=HTMLUtil.toLabel(TaisyoData.getStatus(bean.getTaisyoData()).toString())%></td>
									<td class="list1_nohin" align="center"><%=HTMLUtil.toLabel(NohinSyoriKb.getStatus(bean.getNohinSyoriKb()).toString())%></td>
									<td class="list1_nohin_dt" align="center"><%=HTMLUtil.toDate(bean.getNohinDt(), "yyyy/MM/dd")%></td>
									<td class="list1_denpyo_qt" class="numeric_label"><%=HTMLUtil.toLabel(bean.getDenpyoQtString(), "#,##0")%></td>
									<td class="list1_genka_kei" class="numeric_label"><%=HTMLUtil.toLabel(bean.getGenkaKeiVlString(), "#,##0")%></td>
									<td class="list1_baika_kei" class="numeric_label"><%=HTMLUtil.toLabel(bean.getBaikaKeiVlString(), "#,##0")%></td>
								</tr>
								<%
									}
								%>
							</table>
						</div>
					</div>

					<div class="term">
						<table class="term_guide">
							<tr>
								<td>
									<font color="#0000FF">
										<b>納品</b>：未確定＝受注データ未確定&emsp;確定済＝受注データ確定済&emsp;出力済＝納品リスト出力済
									</font>
								</td>
							</tr>
						</table>
					</div>
					<br />

					<div class="term">
						<table class="term_btn_area">
							<tr>
								<td align="center">
									<input type="button" name="" value="一括確定" class="controlButton" onClick="decideCommand();" />
									<font color="#0000FF">※納品が「未確定」のデータを一括確定します。</font>
								</td>
							</tr>
						</table>
					</div>
					<br />

					<div class="term">
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>リスト出力対象</th>
								<td align="center">
									<label><input type="radio" name="term_list_target" value="0" <%=HTMLUtil.toRadioCheck(nohinAllStatus.getListTarget().getCode(), ListTarget.ALL.getCode())%> /><span><%=ListTarget.ALL.toString()%></span></label>&emsp;
									<label><input type="radio" name="term_list_target" value="1" <%=HTMLUtil.toRadioCheck(nohinAllStatus.getListTarget().getCode(), ListTarget.MI_ONLY.getCode())%> /><span><%=ListTarget.MI_ONLY.toString()%></span></label>
								</td>
							</tr>
						</table>
						<table class="term_btn_area">
							<tr>
								<td align="center">
									<input type="button" name="" value="納品リスト出力" class="controlButton" onClick="listCommand();" />
								</td>
							</tr>
						</table>
					</div>
					<%
					}
					%>
					<!------ 検索結果  END  --------------------------------------------------------------------------------------------------------------------------------------------------------->

					<!---- システム共通フッター START ----------------------------------------------------------------------------------------------------------------------------------------------->
					<div class="term">
						<table border="0" cellspacing="1" cellpadding="0" class="term_note">
							<tr>
								<td>
									<br />
									&emsp;<font color="#0000FF"><b>※検索について</b><br />
									&emsp;　納品日（自）のみ指定されている場合は、指定された納品日のみが対象となります。<br />
									&emsp;&emsp;</font>
									<br />
									&emsp;<font color="#0000FF"><b>※「納品リスト」について</b><br />
									&emsp;1.納品日は(自)に入力されている１日のみ出力対象となります。<br />
									&emsp;2.未出力のリストのみ出力したい場合は、リスト出力対象の「未出力のみ」を選択し、<br />
									&emsp;&emsp;各リストボタンを押してください。</font>
									<br />
									<br />
								</td>
							</tr>
						</table>
					</div>
					<!---- システム共通フッター END ------------------------------------------------------------------------------------------------------------------------------------------------->
				</td>
			</tr>
		</table>
	</form>
</body>