<body bgcolor="#FFFFFF" text="#000000" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" onload="init();putOnLoadDisplay();<% if(dataChk!=null && dataChk.equals("OK")){ out.print("downloadFile()"); }%>">
	<table width="100%" border="0" cellspacing="0" cellpadding="0" height="100%">	
		<!------ システム共通ヘッダー  START ------>
		<jsp:include page="ptl000001_Header.jsp?PARAM=送信一覧（SSN04012）"></jsp:include>
		<tr height="5"></tr>
		<!------ システム共通ヘッダー  END ------>
  		<tr>
			<td valign="top">
				<div align="center">
					<form name="MainForm" action="app" method="post">
						<input type="hidden" name="JobID" value="">
						<input type="hidden" name="ModifiedCondition" value="">
						<input type="hidden" name="moveTo" />
						<table width="950" border="0" cellspacing="1" cellpadding="2" class="kensaku">
							<tr>
	<%if(MenuPattern.CENTER.getCode().equals(menuTransition)){%>
		<%if(RoleUtil.isCenterFurumai(role)){%>
								<th>経由センタ</th>
								<td align="left" style="width: 382px;">
									<input type="text" name="center_cd" value="<%= HTMLUtil.toText(dataHolder.getParameter("center_cd")) %>" style="width: 50px;"size="8" maxlength="<%=centerCdLen %>" id="no_input_text" tabindex="-1"readOnly>
									<input type="text" name="center_na" value="<%= HTMLUtil.toText(dataHolder.getParameter("center_na")) %>" style="width: 210px;"size="40" id="no_input_text" tabindex="-1" readOnly>
								</td>	
		<%}else{%>
								<th>*経由センタ</th>
								<td align="left" style="width: 382px;">
									<input type="text" name="center_cd" size="8" maxlength="<%=centerCdLen %>" value="<%= HTMLUtil.toText(dataHolder.getParameter("center_cd")) %>" style="ime-mode:disabled; width: 52px;" />
									<input type="text" name="center_na" value="<%= HTMLUtil.toText(dataHolder.getParameter("center_na")) %>" size="40" style="width: 110px;" id="no_input_text" tabindex="-1" readOnly>
									<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_centerSel('MainForm.center_cd','MainForm.center_na');" />
									<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.center_cd, MainForm.center_na);" />				
								</td>
		<%}%>
	<%}else{%>
		<%if(RoleUtil.isTorihikisakiFurumai(role)){%>
								<th>取引先</th>
								<td align="left" style="width: 382px;">
									<input type="text" name="torihikisaki_cd" value="<%= HTMLUtil.toText(dataHolder.getParameter("torihikisaki_cd")) %>" style="width: 50px;" size="8" maxlength="<%=torihikisakiCdLen %>" id="no_input_text" tabindex="-1"readOnly>
									<input type="text" name="torihikisaki_na" value="<%= HTMLUtil.toText(dataHolder.getParameter("torihikisaki_na")) %>" style="width: 210px;" size="40" id="no_input_text" tabindex="-1" readOnly>
								</td>
		<%}else{%>					
								<th>*取引先</th>
							    <td align="left" style="width: 382px;">
									<input type="text" name="torihikisaki_cd" size="8" maxlength="<%=torihikisakiCdLen %>" value="<%= HTMLUtil.toText(dataHolder.getParameter("torihikisaki_cd")) %>" style="ime-mode:disabled; width: 52px;" />
							        <input type="text" name="torihikisaki_na"  value="<%= HTMLUtil.toText(dataHolder.getParameter("torihikisaki_na")) %>" id="no_input_text" tabindex="-1" readonly style="width: 110px;" />
									<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_siireSel('MainForm.torihikisaki_cd','MainForm.torihikisaki_na');" />
									<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.torihikisaki_cd, MainForm.torihikisaki_na);" />				
								</td>
		<%}%>
	<%}%>
								<th nowrap>送信日</th>
								<td colspan="1" style="width: 323px;">
									<input type="text" name="send_dt_from" maxlength="8" size="10" style="width: 62px;" value="<% if( dataHolder.getParameter("send_dt_from") != null ) out.print(dataHolder.getParameter("send_dt_from"));%>">
									<img src="images/calendar.gif" width="18" height="18" align="absmiddle" onClick="callCalendar( document.MainForm, document.MainForm.send_dt_from );">(自)&nbsp;
									～
									<input type="text" name="send_dt_to" size="10" maxlength="8" style="width: 62px;" value="<% if( dataHolder.getParameter("send_dt_to") != null ) out.print(dataHolder.getParameter("send_dt_to"));%>">
									<img src="images/calendar.gif" width="18" height="18" align="absmiddle" onClick="callCalendar( document.MainForm, document.MainForm.send_dt_to );">(至)
									<small>(YYYYMMDD)</small>
								</td>
							</tr>
							<tr>
								<th rowspan="2" nowrap>*処理状況</th>
								<td rowspan="2" >
									<label><input type="checkbox" name="syori_jokyo_kb_1" onClick="changeModifiedCondition();" value="1" <% if( dataHolder.getParameter("syori_jokyo_kb_1") != null && dataHolder.getParameter("syori_jokyo_kb_1").trim().length() > 0) out.print("checked"); %>><span>送信済</span></label>&emsp;&nbsp;&nbsp;
									<label><input type="checkbox" name="syori_jokyo_kb_2" onClick="changeModifiedCondition();" value="2" <% if( dataHolder.getParameter("syori_jokyo_kb_2") != null && dataHolder.getParameter("syori_jokyo_kb_2").trim().length() > 0 ) out.print("checked"); %>><span>処理中</span></label>&emsp;&nbsp;&nbsp;
									<label><input type="checkbox" name="syori_jokyo_kb_3" onClick="changeModifiedCondition();" value="3" <% if( dataHolder.getParameter("syori_jokyo_kb_3") != null && dataHolder.getParameter("syori_jokyo_kb_3").trim().length() > 0 ) out.print("checked"); %>><span>処理済</span></label>&emsp;
									<label><input type="checkbox" name="syori_jokyo_kb_4" onClick="changeModifiedCondition();" value="4" <% if( dataHolder.getParameter("syori_jokyo_kb_4") != null && dataHolder.getParameter("syori_jokyo_kb_4").trim().length() > 0 ) out.print("checked"); %>><span>処理済(警告あり)</span></label>&emsp;
									<br>
									<label><input type="checkbox" name="syori_jokyo_kb_5" onClick="changeModifiedCondition();" value="5" <% if( dataHolder.getParameter("syori_jokyo_kb_5") != null && dataHolder.getParameter("syori_jokyo_kb_5").trim().length() > 0 ) out.print("checked"); %>><span>送信エラー</span></label>
									<label><input type="checkbox" name="syori_jokyo_kb_6" onClick="changeModifiedCondition();" value="6" <% if( dataHolder.getParameter("syori_jokyo_kb_6") != null && dataHolder.getParameter("syori_jokyo_kb_6").trim().length() > 0 ) out.print("checked"); %>><span>処理エラー</span></label>
									<label><input type="checkbox" name="syori_jokyo_kb_7" onClick="changeModifiedCondition();" value="7" <% if( dataHolder.getParameter("syori_jokyo_kb_7") != null && dataHolder.getParameter("syori_jokyo_kb_7").trim().length() > 0 ) out.print("checked"); %>><span>削除</span></label>&emsp;
								</td>
								<th nowrap>データ種別</th>
								<td>
									<select name="syubetu" size="1" >
<%
				Iterator dataite = dataSyuBh.getBeanList().iterator();
				while( dataite.hasNext() ){
					RDataSyubetuBean bean = (RDataSyubetuBean)dataite.next();
%>
										<option value="<%= bean.getDataSyubetuCd() %>"

<%
				if(dataHolder.getParameter("syubetu") != null){
				if(dataHolder.getParameter("syubetu").trim().equals(bean.getDataSyubetuCd().trim())){ 
					out.print(" selected ");
				}
			}
%>>
			<%= bean.getDataSyubetuNa() %>
										</option>
<%
		}
%>
									</select>
								</td>
							</tr>
							</tr>
							<tr>
								<td colspan="2">&nbsp;
								</td>
							</tr>
						</table>
						<br>
						
						<input type="button" style="width: 97px;" name="search" value="&emsp;検&emsp;索&emsp;" class="btn" onClick="searchCommand();"/>						
						<input type="button" style="width: 92px;" name="back" value="&emsp;戻&emsp;る&emsp;" class="btn" onClick="exitCommand();" />   

					<br><br>
					<jsp:include page="InfoStringMdWare.jsp" />
					<br>
<%
	if (uploadList.getBeanList() != null && uploadList.getBeanList().size() > 0) {
	
%>

						<table width="980" border="0" cellspacing="1" cellpadding="0" class="data" style="line-height: 14px;">
							<tr>
								<th nowrap style="width: 39px;" align="center">No</th>
								<th nowrap style="width: 49px;" align="center">選択</th>
								<th nowrap style="width: 126px;" align="center">処理<br>状況</th>
								<th nowrap style="width: 97px;" align="center">データ種別</th>
								<th nowrap style="width: 291px;" align="center">ファイル名</th>
								<th nowrap style="width: 68px;" align="center">サイズ<br>(byte) </th>
								<th nowrap style="width: 146px;" align="center">送信日時</th>
								<th nowrap style="width: 155px;" align="center">処理日時</th>
							</tr>
						</table>
						<div style="overflow:scroll;overflow-x:hidden; width: 980px; height: 150px;">
							<table border="0" cellspacing="1" cellpadding="0" class="data" width="980">
<%
	int dispNum = uploadList.getStartRowInPage();
	Iterator ite = uploadList.getBeanList().iterator();
	while( ite.hasNext() )
	{
		DataExchangeUploadListBean bean = (DataExchangeUploadListBean)ite.next();
		UploadSyoriJokyoKb dic = UploadSyoriJokyoKb.getStatus(bean.getSyoriJokyoKb());
		if( dic.equals(UploadSyoriJokyoKb.SYORI_ERROR) || dic.equals(UploadSyoriJokyoKb.SYORI_WARN)){
%>
								<tr class="err">
<%								
		}else if( dic.equals(UploadSyoriJokyoKb.SYORI_CHU) ){
%>
								<tr class="run">
								
<%		}else{%>
								<tr>
<%		}%>
									<input type="HIDDEN" name="syori_jokyo_<%=dispNum%>" value="<%=bean.getSyoriJokyoKb()%>">
									<td align="right"  style="width: 39px;"><%=dispNum%></td>									
									<td align="center" style="width: 49px;" >									
										<input type="checkbox" name="del<%=bean.getFileHeadNb()%>" value="<%=dispNum%>"
				<%
					if( !dic.equals(UploadSyoriJokyoKb.SOSIN_ERROR) && !dic.equals(UploadSyoriJokyoKb.SYORI_WARN) && !dic.equals(UploadSyoriJokyoKb.SYORI_ERROR))
						out.print(" disabled");
				%>
										>
									</td>
									<td align="center" style="width: 126px;"><%=dic.toString()%></td>
									<td align="center" style="width: 97px;" ><%=HTMLUtil.toLabel(bean.getDataSyubetuNa())%></td>
									<td align="left" style="width: 291px;">
										<img src="images/folder.gif" width="15" height="15"><%=(new File(bean.getClientFileNa())).getName()%>
									</td>
									<td align="right"  style="width: 68px;" ><%=CurrencyUtility.format(CurrencyUtility.KANMA_FORMAT, bean.getFileLengthQt())%></td>
									<td align="center" style="width: 146px;"><%=DateChanger.toSeparatorTimeStamp(bean.getSendTs())%></td>
									<td align="center" style="width: 155px;"><%=DateChanger.toSeparatorTimeStamp(bean.getUpdateTs())%></td>
								</tr>
<%
		dispNum++;
	}
%>
		  					</table>
		  				</div>
		  				<div style="font-size:12px;margin-top:5px;">
		  				<table border="0" class="padding">
		  				<tr>
		  					<th align="left" rowspan="2" >処理状況</th>
		  					<td align="left">[送信済]</td>
		  					<td align="left">：ファイル送信済</td>		  					
		  					<td align="left">[処理中]</td>
		  					<td align="left">：ファイル取込中</td>
		  					<td align="left">[処理済(警告あり)]</td>
		  					<td align="left">：ファイル取込済（警告あり）</td>
		  					<td align="left">[処理済]</td>
		  					<td align="left">：ファイル取込済</td>		  					
		  				</tr>
		  				<tr>
		  					<td align="left">[送信エラー]</td>
		  					<td align="left">：ファイル送信時にエラー</td>
		  					<td align="left">[処理エラー]</td>
		  					<td align="left">：ファイル取込時にエラー</td>
		  					<td align="left">[削除]</td>
		  					<td align="left">：削除済</td>
		  					<td></td>
		  				</tr>		  				
		  				</table>
		  				
		  				</div>
						<br>
<%
if( uploadList.getCurrentPageNumber() == 1 )
	out.print("<input type=\"button\" value=\"先頭\" disabled onClick=\"changePage('first');\" style=\"width: 38px; height: 21px;\" />");
else
{
	out.print("<input type=\"button\" value=\"先頭\" onClick=\"changePage('first');\" style=\"width: 38px; height: 21px;\" />");
}
%>
　
<%
if( uploadList.getCurrentPageNumber() == 1 )
	out.print("<input type=\"button\" value=\"前の" + rowInPage +"件\" disabled onClick=\"changePage('prev');\" style=\"width: 76px; height: 21px;\" />");
else
{
	out.print("<input type=\"button\" value=\"前の" + rowInPage +"件\" onClick=\"changePage('prev');\" style=\"width: 76px; height: 21px;\" />");
}
%>
　
<%
if( uploadList.getCurrentPageNumber() == uploadList.getLastPageNumber() || uploadList.getLastPageNumber() == 0)
	out.print("<input type=\"button\" value=\"次の" + rowInPage + "件\" disabled onClick=\"changePage('next');\" style=\"width: 76px; height: 21px;\"  />");
else
{
	out.print("<input type=\"button\" value=\"次の" + rowInPage + "件\" onClick=\"changePage('next');\" style=\"width: 76px; height: 21px;\" />");
}
%>
　
<%
if( uploadList.getCurrentPageNumber() == uploadList.getLastPageNumber() || uploadList.getLastPageNumber() == 0)
	out.print("<input type=\"button\" value=\"最終\" disabled onClick=\"changePage('last');\" style=\"width: 38px; height: 21px;\" />");
else
{
	out.print("<input type=\"button\" value=\"最終\" onClick=\"changePage('last');\" style=\"width: 38px; height: 21px;\" />");
}
%>
						<br>
		  （<%=uploadList.getMaxRows()%>件中<%=uploadList.getStartRowInPage()%>～<%=uploadList.getEndRowInPage()%>件目） <br>
						<br>
						<input type="BUTTON" name="submit2" style="width: 113px;" value="　ダウンロード　" class="btn" onclick="downloadCommand();">
						<input type="button" name="cancel" style="width: 106px;" value="　　削　除　　" class="btn" onclick="deleteCommand();">
					</form>
					
					<!--ファイルダウンロード専用フォーム-->
					<form name="download" action="download" method="post">
						<input type="hidden" name="svfilename">
						<input type="hidden" name="downloadFileNa" >
					</form>
					
				</div>
				<table border="2" cellspacing="0" cellpadding="0" class="kensaku tableNote" width="650" align="center">
					<tr>
						<td>
							<br>&emsp;<font color="#0000FF"><b>※ダウンロードについて</b><br/>
								&emsp;　「処理エラー」、「処理済（警告あり）」のみダウンロード可能です。複数選択不可。<br />
								&emsp;　ダウンロードデータのエラー内容を修正後、ファイル送信画面より再度アップロードして下さい。<br />
								&emsp;&emsp;</font>

						
							<br>&emsp;<font color="#0000FF"><b>※検索について</b><br/>
								&emsp;　送信日（自）のみ指定されている場合は、指定された送信日のみが対象となります。
								&emsp;&emsp;</font>
							<br>&emsp;
						</td>
						<td style="width: 3px; border-width: 0px;"></td>
					</tr>
					<tr style="height: 1px;"></tr>
				</table>
<%
	}
%>
			</td>
		</tr>
	</table>
</body>