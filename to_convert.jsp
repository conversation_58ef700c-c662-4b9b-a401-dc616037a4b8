<body bgcolor="#FFFFFF" text="#000000" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" onLoad="init();putOnLoadDisplay();">
<table width="100%" border="0" cellspacing="0" cellpadding="0" height="100%">

<!------ システム共通ヘッダー  START ------>
	<jsp:include page="ptl000001_Header.jsp?PARAM=納品登録（SSN04003）"></jsp:include>
<!------ システム共通ヘッダー  END ------>

<!------ システム共通メニューバー  START ------>
<!------ システム共通メニューバー  END ------>

<!------ Body START ------>
<form name="MainForm" method="post" action="app">
<jsp:include page="rbs00000_common.jsp" flush="true" />
<input type="hidden" name="Modified" value="">
<input type="hidden" name="ModifiedCondition" value="">

	<tr>
		<td align="center" valign="top">

	<table border="0" cellspacing="1" cellpadding="0" class="kensaku">
		<tr>
			<th>取引先</th>
			<td nowrap colspan="3" class="string_label">
				<input type="text" name="torihikisaki_cd" style="width: 55px;" size="9" value="<%= HTMLUtil.toText(jutyuItiranBean.getTorihikisakiCd()) %>" id="no_input_text" tabindex="-1" readonly />
				<input type="text" name="torihikisaki_na" style="width: 160px;" size="30" value="<%= HTMLUtil.toText(jutyuItiranBean.getTorihikisakiNa()) %>" id="no_input_text" tabindex="-1" readonly />
			</td>		
			<th>伝票番号</th>
			<td nowrap class="string_label">
				<input type="text" name="denpyo_nb" size="12" style="width: 70px;" value="<%= HTMLUtil.toText(jutyuItiranBean.getDenpyoNbString()) %>" id="no_input_text" tabindex="-1" readonly />
			</td>
			<th>店舗</th>
			<td nowrap class="string_label">
				<input type="text" name="tenpo_cd" value="<%= HTMLUtil.toText(jutyuItiranBean.getTenpoCd()) %>"size="6" id="no_input_text" tabindex="-1" style="ime-mode:disabled; width: 40px;" />
				<input type="text" name="tenpo_na" value="<%= HTMLUtil.toText(jutyuItiranBean.getTenpoNa()) %>" id="no_input_text" tabindex="-1" readonly style="width: 110px;" />
			</td>			
			</td>
		</tr>
			<th>納品区分</th>
			<td nowrap class="string_label" width="100">
				<input type="text" size="13" style="width: 75px;" value="<%= HTMLUtil.toLabel(jutyuItiranBean.getButuryuSn()) %>" id="no_input_text" tabindex="-1" readonly />
			</td>		
			<th>発注日</th>
			<td nowrap class="string_label" width="100">
				<input type="text" name="hachu_dt" size="13" style="width: 75px;" value="<%= HTMLUtil.toDate(jutyuItiranBean.getHachuDt(), "yyyy/MM/dd")  %>" id="no_input_text" tabindex="-1" readonly />
			</td>			
			<th>納品予定日</th>
			<td nowrap class="string_label" width="100">
				<input type="text" name="nohin_dt" size="13" style="width: 75px;" value="<%= HTMLUtil.toDate(jutyuItiranBean.getNohinDt(), "yyyy/MM/dd")  %>" id="no_input_text" tabindex="-1" readonly />
			</td>
			<th>実納品日</th>
			<td nowrap class="string_label" width="200">
				<input type="text" name="zitu_nohin_dt" size="13" value="<%= HTMLUtil.toText(jutyuItiranBean.getZituNohinDtForNohinToroku()) %>" maxlength="8" <%= jutyuItiranBean.isKakutei() == true ? "id=\"no_input_text\" tabindex=\"-1\" readonly style=\"ime-mode:disabled; width: 75px;\" " : "style=\"ime-mode:disabled; width: 77px;\"" %> />
<%	if( jutyuItiranBean.isKakutei() == false ) {	%>
				<img src="./images/calendar.gif" width="18" height="18" align="absmiddle" alt="日付選択" onClick="callCalendar(MainForm, MainForm.zitu_nohin_dt);" />
				<small>（YYYYMMDD）</small>
<%	}	%>		
		</tr>
		<tr>
			<th nowrap>便</th>
			<td nowrap class="string_label" width="100">
				<input type="text" name="bin_nm" size="3" style="width: 25px;" value="<%= HTMLUtil.toText(jutyuItiranBean.getBinNm(), "#") %>"id="no_input_text" class="numeric" tabindex="-1" readonly />
			</td>
		</tr>
	</table>

	<br />

	<jsp:include page="InfoStringMdWare.jsp" />

	<br />
	<input type="hidden" name="GEN01_FUTOGO" value="<%= wkGenkaCheck.get("GEN01") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN01")).getFutogo() : "" %>">
	<input type="hidden" name="GEN01_VALUE"  value="<%= wkGenkaCheck.get("GEN01") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN01")).getValueString() : "" %>" >
	<input type="hidden" name="GEN01_MSG"    value="<%= wkGenkaCheck.get("GEN01") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN01")).getClMsg() : "" %>" >
	<input type="hidden" name="GEN02_FUTOGO" value="<%= wkGenkaCheck.get("GEN02") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN02")).getFutogo() : "" %>">
	<input type="hidden" name="GEN02_VALUE"  value="<%= wkGenkaCheck.get("GEN02") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN02")).getValueString() : "" %>" >
	<input type="hidden" name="GEN02_MSG"    value="<%= wkGenkaCheck.get("GEN02") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN02")).getClMsg() : "" %>" >
	<input type="hidden" name="GEN03_FUTOGO" value="<%= wkGenkaCheck.get("GEN03") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN03")).getFutogo() : "" %>">
	<input type="hidden" name="GEN03_VALUE"  value="<%= wkGenkaCheck.get("GEN03") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN03")).getValueString() : "" %>" >
	<input type="hidden" name="GEN03_MSG"    value="<%= wkGenkaCheck.get("GEN03") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN03")).getClMsg() : "" %>" >
	<input type="hidden" name="GEN04_FUTOGO" value="<%= wkGenkaCheck.get("GEN04") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN04")).getFutogo() : "" %>">
	<input type="hidden" name="GEN04_VALUE"  value="<%= wkGenkaCheck.get("GEN04") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN04")).getValueString() : "" %>" >
	<input type="hidden" name="GEN04_MSG"    value="<%= wkGenkaCheck.get("GEN04") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN04")).getClMsg() : "" %>" >
	
<%	if( jutyuTorokuBh.getMaxRows() > 0 ) {	%>

	<table border="0" cellspacing="0" cellpadding="0" class="data">
		<tr align = "center">
			<td >
			<div align = "left" style="width: 918px;">				
			<table border="0" cellspacing="1" cellpadding="0" class="data" style="line-height: 14px;">
				<tr>		
					<th nowrap width="15"></th>
					<th nowrap width="220" align="center">
						<table border="0">
							<tr nowrap>
								<th nowrap align="left" width="100">商品コード</th>
								<th nowrap align="left" width="50">入数</th>
								<th nowrap align="left" width="50">発単</th>
							</tr>
							<tr nowrap>
								<th nowrap align="left" colspan="3">商品名</th>
							</tr>
						</table>
					</th>
					<th nowrap width="50" align="center">発注<br />数</th>
					<th nowrap width="70" align="center">発注<br />数量</th>
					<th nowrap width="50" align="center">訂正後<br />納品数</th>
					<th nowrap width="70" align="center">訂正後<br />納品数量</th>
					<th nowrap width="90" align="center">原単価</th>
					<th nowrap width="70" align="center">売単価</th>
					<th nowrap width="95" align="center">産地</th>
					<th nowrap width="80" align="center">等級</th>
					<th nowrap width="80" align="center">規格</th>			
					<th nowrap width="17" align="center"></th>
					
				</tr>
			</table>
			</div>				
			</td>
		</tr>
		<tr align = "center">
			<td >				
			<div align = "left" style="overflow:scroll;overflow-x:hidden; width: 920px;height: 300px; margin-left: 2px;">
			<table border="0" cellspacing="1" cellpadding="0" class="data">
			
		<%
				// 明細表示
				int i = 0;
				BigDecimal genkaKeiVl = new BigDecimal("0");
				BigDecimal baikaKeiVl = new BigDecimal("0");
				for( Iterator ite = jutyuTorokuBh.getBeanIterator(); ite.hasNext(); i++ ) {
					NohinTorokuBean bean = (NohinTorokuBean)ite.next();
					genkaKeiVl = genkaKeiVl.add(new BigDecimal(bean.getKakuGenkaVlString()));
					baikaKeiVl = baikaKeiVl.add(new BigDecimal(bean.getKakuBaikaVlString()));
		%>
				<tr>
					<input type="hidden" name="ido_gentanka_vl_<%= i %>" value="<%= HTMLUtil.toText(bean.getIdoGentankaVl(), "0.00") %>" />
					<input type="hidden" name="ido_baitanka_vl_<%= i %>" value="<%= HTMLUtil.toText(bean.getIdoBaitankaVl(), "#") %>" />				
					<input type="hidden" name="isTeikan_<%= i %>" value="<%= bean.isTeikan() %>" />														
					<%-- データ伝票行番号 --%>
					<td nowrap width="11" class="numeric_label"><%= HTMLUtil.toLabel(bean.getDataDenpgyoNbString()) %></td>
					<td nowrap width="220">
						<table style="height: 46px;">
							<tr>
								<%-- 商品コード --%>
								<td nowrap align="left" width="100"><input type="text" size="17" value="<%= HTMLUtil.toLabel(bean.getHachuSyohinCd()) %>" style="border-width: 0px; width: 95px;" tabindex="-1" readonly /></td>
								<%-- 入数 --%>
								<td nowrap align="left" width="50"><input type="text" size="5" value="<%= HTMLUtil.toLabel(bean.getIrisuQtString(), "#,##0") %>" style="border-width: 0px; width: 35px;" class="numeric" tabindex="-1" readonly /></td>
								<%-- 発注単位名 --%>
								<td nowrap align="left" width="50"><input type="text" size="6" value="<%= HTMLUtil.toLabel(bean.getHachuTaniNa()) %>" style="border-width: 0px; width: 40px;" tabindex="-1" readonly /></td>
							</tr>
							<tr>
								<%-- 商品名 --%>
								<td nowrap align="left" colspan="3"><input type="text" size="40" value="<%= HTMLUtil.toLabel(bean.getSyohinNa()) %>" style="border-width: 0px; width: 210px;" tabindex="-1" readonly /></td>
							</tr>
						</table>
					</td>
					<%-- 発注数 --%>
					<td nowrap width ="50" align="center"><input type="text" size="5" value="<%= HTMLUtil.toLabel(bean.getHachuQtString(), "#,##0") %>" style="border-width: 0px; width: 35px;" class="numeric" tabindex="-1" readonly /></td>
					<%-- 発注数量 --%>
					<td nowrap width ="70" align="center"><input type="text" size="10" value="<%= HTMLUtil.toLabel(bean.getHachuSuryoQtString(), "#,##0.##") %>" style="border-width: 0px; width: 60px;" class="numeric" tabindex="-1" readonly /></td>
					<%-- 訂正後納品数 --%>
					<td nowrap width ="50" align="center">			
						<input type="hidden" name="hachu_qt_<%= i %>" 		value="<%= bean.getHachuQt() 		%>" />
						<input type="hidden" name="hachu_suryo_qt_<%= i %>" value="<%= bean.getHachuSuryoQt() 	%>" />
						<input type="hidden" name="baika_fix_fg_<%= i %>" 	value="<%= bean.getBaikaFixFg() 	%>" />
						<input type="hidden" name="gen_tan_tani_<%= i %>" 	value="<%= bean.getGenTanTani() 	%>" />
						<input type="hidden" name="teikan_kb_<%= i %>" 		value="<%= bean.getTeikanKb() 		%>" />
						<input type="text" name="kakutei_qt_<%= i %>" 		value="<%= HTMLUtil.toText(bean.getKakuteiQtForText(), "0") %>" size="5" maxlength="4" class="numeric" <%= (jutyuItiranBean.isKakutei() || bean.isKakutei()) ? "id=\"no_input_text\" tabindex=\"-1\" readonly style=\"ime-mode:disabled; width: 35px;\"" : "style=\"ime-mode:disabled; width: 37px;\" onBlur=\"kakuteiSuCheck(this, MainForm.kakutei_suryo_qt_" + i + ", " + bean.getHachuQt() + ", " + bean.getIrisuQt() + ", " + bean.getKanAvgWt() + ", " + bean.isTeikan() + ");\"" %> />
						<%-- 納品数量へのフォーカス制御の為のダミー --%> 
						<input type="button" style="width:0px; border-width: 0px; " onFocus="meisaiKakuteiQtFocusControl(kakutei_qt_<%= i %>, kakutei_suryo_qt_<%= i %>);" <%= ( jutyuItiranBean.isKakutei() == true || bean.isKakutei() == true || bean.isTeikan() == true ) ? "tabindex=\"-1\"" : ""  %> readonly>											
					</td>
					<%-- 訂正後納品数量 --%>
					<td nowrap width ="70" align="center">
						<input type="text" name="kakutei_suryo_qt_<%= i %>" value="<%= HTMLUtil.toText(bean.getKakuteiSuryoQtForText(), "0.##") %>" size="10" maxlength="9" class="numeric"
						<%= ( jutyuItiranBean.isKakutei() == true || bean.isKakutei() == true || bean.isTeikan() == true ) ? "id=\"no_input_text\" tabindex=\"-1\" readonly style=\"ime-mode:disabled; width: 60px;\"" : "onBlur=\"kakuteiSuryoCheck(this);\" style=\"ime-mode:disabled; width: 62px;\"" %> />
					</td>
					<%-- 原単価 --%>
					<td nowrap width ="90" align="center"><input type="text" name="kaku_gentanka_vl_<%= i %>" value="<%= HTMLUtil.toText(bean.getKakuGentankaVl(), "0.00") %>" size="12" maxlength="10" class="numeric" <%= ( jutyuItiranBean.isKakutei() == true || bean.isKakutei() == true || tankaChgFg == false ) ?  "id=\"no_input_text\" tabindex=\"-1\" readonly style=\"width: 70px;\"" : "style=\"width: 72px;\" onBlur=\"genkaBaikaSumCalc();\"" %>/></td>
					<%-- 売単価 --%>
					<td nowrap width ="70" align="center"><input type="text" name="kaku_baitanka_vl_<%= i %>" value="<%= HTMLUtil.toText(bean.getKakuBaitankaVlStringForDisp(), "#") %>" size="8" maxlength="7" class="numeric" <%= ( jutyuItiranBean.isKakutei() == true || bean.isKakutei() == true || tankaChgFg == false ) ?  "id=\"no_input_text\" tabindex=\"-1\" readonly style=\"width: 50px;\"" : "style=\"width: 52px;\" onBlur=\"genkaBaikaSumCalc();\"" %>/></td>
					<%-- 産地 --%>
					<td nowrap width ="95" align="center">
						<input type="hidden" name="kaku_santi_cd_<%= i %>" value="<%= HTMLUtil.toText(bean.getKakuSantiCd()) %>" />
						<input type="text" name="kaku_santi_na_<%= i %>" size="6" value="<%= HTMLUtil.toText(bean.getKakuSantiNa()) %>" style="width: 40px;" id="no_input_text" tabindex="-1" readonly />
		<%			if( jutyuItiranBean.isKakutei() == false && bean.isKakutei() == false && jutyuItiranBean.isSoba() == true ) {	%>
						<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_santiSel('MainForm.kaku_santi_cd_<%= i %>','MainForm.kaku_santi_na_<%= i %>','<%= jutyuItiranBean.getTorihikisakiCd() %>')"/>
		<%			} else {	%>
						<img src="./images/spacer.gif" width="40" height="20" align="absmiddle" />
		<%			}	%>
					</td>
					<%-- 等級 --%>
					<td nowrap width ="80" align="center">
						<input type="hidden" name="kaku_tokaikyu_cd_<%= i %>" value="<%= HTMLUtil.toText(bean.getKakuTokaikyuCd()) %>" />
						<input type="text" name="kaku_tokaikyu_na_<%= i %>" size="3" style="width: 25px;" value="<%= HTMLUtil.toText(bean.getKakuTokaikyuNa()) %>" id="no_input_text" tabindex="-1" readonly />
		<%			if( jutyuItiranBean.isKakutei() == false && bean.isKakutei() == false && jutyuItiranBean.isSoba() == true ) {	%>
						<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_tokaiqSel('MainForm.kaku_tokaikyu_cd_<%= i %>','MainForm.kaku_tokaikyu_na_<%= i %>','<%= jutyuItiranBean.getTorihikisakiCd() %>')"/>
		<%			} else {	%>
						<img src="./images/spacer.gif" width="40" height="20" align="absmiddle" />
		<%			}	%>
					</td>
					<%-- 規格 --%>
					<td nowrap width ="80" align="center">
						<input type="hidden" name="kaku_kikaku_cd_<%= i %>" value="<%= HTMLUtil.toText(bean.getKakuKikakuCd()) %>" />
						<input type="text" name="kaku_kikaku_na_<%= i %>" size="3" style="width: 25px;" value="<%= HTMLUtil.toText(bean.getKakuKikakuNa()) %>" id="no_input_text" tabindex="-1" readonly />
		<%			if( jutyuItiranBean.isKakutei() == false && bean.isKakutei() == false && jutyuItiranBean.isSoba() == true ) {	%>
						<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_kikakuSel('MainForm.kaku_kikaku_cd_<%= i %>','MainForm.kaku_kikaku_na_<%= i %>','<%= jutyuItiranBean.getTorihikisakiCd() %>')"/>
		<%			} else {	%>
						<img src="./images/spacer.gif" width="40" height="20" align="absmiddle" />
		<%			}	%>
					</td>
				</tr>
<%		}	%>
			</table>
			</div>
			</td>
		</tr>
		<tr>
			<td>
			<br>
			<table width="920" border="0" cellspacing="0" cellpadding="0">
				<td align="right">
					<table border="0" cellspacing="1" cellpadding="0" class="shori_info">
						<th style="width: 95px;">原価金額合計</th>
						<td height="22" width="100" align="center">
							<input type="text" name="genka_kei_vl" size="16" style="width: 90px;" value="<%= HTMLUtil.toText(genkaKeiVl.doubleValue(), "#,##0") %>" class="numeric" id="no_input_text" tabindex="-1" readonly />
						</td>
						<th style="width: 95px;">売価金額合計</th>
						<td height="22" width="100" align="center">
		<%		if( baikaKeiVl.doubleValue() == 0D ) {	// 売価金額合計が０の場合、非表示	%>
							<input type="text" name="baika_kei_vl" size="16" style="width: 90px;" value="" class="numeric" id="no_input_text" tabindex="-1" readonly />
		<%		} else {	%>
							<input type="text" name="baika_kei_vl" size="16" style="width: 90px;" value="<%= HTMLUtil.toText(baikaKeiVl, "#,###") %>" class="numeric" id="no_input_text" tabindex="-1" readonly />
		<%		}	%>
						</td>
					</table>
				</td>
			</table>
			</td>
		</tr>
	</table>
<%	}	%>

	<br />

	<table align="center" width="70%" border="0" cellspacing="0" cellpadding="0">
		<td align="left">
			<input type="button" value="受注～納品確定" class="btn" onClick="returnCommand();" style="width: 136px;"/>
		</td>
		<td align="right">
<%	if( jutyuTorokuBh.getMaxRows() > 0 && jutyuItiranBean.isKakutei() == false ) {	%>
			<input type="button" value="&nbsp;登&emsp;録&nbsp;" class="btn" onClick="updateCommand();" style="width: 70px;"/>
<%	} else {	%>
			<input type="button" value="&nbsp;登&emsp;録&nbsp;" class="btn" disabled style="width: 70px;"/>
<%	}	%>
		</td>
	</table>

</form>
<!-- Body END -->
<!---- システム共通フッター START ---->
<!---- システム共通フッター END ---->
</table>
</body>