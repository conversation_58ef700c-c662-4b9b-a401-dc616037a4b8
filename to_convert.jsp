<body bgcolor="#FFFFFF" text="#000000" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" onLoad="init();putOnLoadDisplay(); outputList();">
<table width="100%" border="0" cellspacing="0" cellpadding="0" height="100%">

<!------ システム共通ヘッダー  START ------>
	<jsp:include page="ptl000001_Header.jsp?PARAM=納品予定‐納品確定（SSN05002）"></jsp:include>
	<tr height="5"></tr>
<!------ システム共通ヘッダー  END ------>

<!------ システム共通メニューバー  START ------>
<!------ システム共通メニューバー  END ------>

<!------ Body START ------>
<form name="MainForm" method="post" action="app">
<jsp:include page="rbs00000_common.jsp" flush="true" />
<input type="hidden" name="Modified" value="">
<input type="hidden" name="ModifiedCondition" value="">
<input type="hidden" name="outPutFlg" value="<%= outPutFlg %>" />

	<tr>
		<td align="center" valign="top">

	<table border="0" cellspacing="0" cellpadding="0">
		<tr>
			<td>
	<table border="0" cellspacing="1" cellpadding="0" class="kensaku">
		<tr>
			<th style="width:80px">*経由センタ</th>
			<td nowrap>
		        <%
				//センター権限振分
				if(RoleUtil.isCenterFurumai(role)){
				%>
					<input type="text" name="center_cd" value="<%= HTMLUtil.toText(nohinItiranStatus.getCenterCd()) %>" style="width: 50px;" size="8" maxlength="<%=centerCdLen %>" id="no_input_text" tabindex="-1"readOnly>
					<input type="text" name="center_na" value="<%= HTMLUtil.toText(nohinItiranStatus.getCenterNa()) %>" style="width: 110px;" id="no_input_text" tabindex="-1" readonly />
			    <%}else{%>
					<input type="text" name="center_cd" value="<%= HTMLUtil.toText(nohinItiranStatus.getCenterCd()) %>" size="8" maxlength="<%=centerCdLen %>" style="ime-mode:disabled; width: 52px;" />
					<input type="text" name="center_na" value="<%= HTMLUtil.toText(nohinItiranStatus.getCenterNa()) %>" id="no_input_text" tabindex="-1" readonly style="width: 110px;" />
					<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_centerSel('MainForm.center_cd','MainForm.center_na');"/>
					<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.center_cd, MainForm.center_na);" />
				<%}%>
			</td>
			<th style="width:80px">*納品日</th>
			<td nowrap style="height: 22px;">
				<input type="text" name="nohin_dt" value="<%= HTMLUtil.toText(nohinItiranStatus.getNohinDt()) %>" size="10" maxlength="8" style="ime-mode:disabled; width: 62px;" />
				<img src="./images/calendar.gif" width="18" height="18" align="absmiddle" alt="日付選択" onClick="callCalendar(MainForm, MainForm.nohin_dt);" />
				<small>（YYYYMMDD）</small>
			</td>
		</tr>
		<tr>
			<th>*取引先</th>
            <td align="left" style="height: 22px;">
                <input type="text" name="torihikisaki_cd" size="8" maxlength="<%=torihikisakiCdLen %>" value="<%= HTMLUtil.toText(nohinItiranStatus.getTorihikisakiCd()) %>" style="ime-mode:disabled; width: 52px;" />
                <input type="text" name="torihikisaki_na"  value="<%= HTMLUtil.toText(nohinItiranStatus.getTorihikisakiNa()) %>" id="no_input_text" tabindex="-1" readonly style="width: 110px;"/>
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_siireSel('MainForm.torihikisaki_cd','MainForm.torihikisaki_na');" />
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.torihikisaki_cd, MainForm.torihikisaki_na);" />
			</td>
			<th>店舗</th>
			<td nowrap>
				<input type="text" name="tenpo_cd" value="<%= HTMLUtil.toText(nohinItiranStatus.getTenpoCd()) %>" size="8" maxlength="<%=tenpoCdLen %>" style="ime-mode:disabled; width: 52px;" />
				<input type="text" name="tenpo_na" value="<%= HTMLUtil.toText(nohinItiranStatus.getTenpoNa()) %>" id="no_input_text" tabindex="-1" readonly style="width: 110px;"/>
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_tenpoSel('MainForm.tenpo_cd','MainForm.tenpo_na','');"/>
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.tenpo_cd, MainForm.tenpo_na);" />
			</td>
		</tr>
		<tr>
			<th>*部門</th>
			<td nowrap style="height: 22px;">
				<input type="text" name="bunrui1_cd" value="<%= HTMLUtil.toText(nohinItiranStatus.getBunrui1Cd()) %>" size="8" maxlength="<%=bunrui1CdLen %>" style="ime-mode:disabled; width: 52px;" />
				<input type="text" name="bunrui1_na" value="<%= HTMLUtil.toText(nohinItiranStatus.getBunrui1Na()) %>" id="no_input_text" tabindex="-1" readonly style="width: 110px;"/>
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_DptLineClass('MainForm.bunrui1_cd','MainForm.bunrui1_na','MainForm.bunrui2_cd','MainForm.bunrui2_na','MainForm.bunrui5_cd','MainForm.bunrui5_na','1',MainForm.bunrui1_cd.value , '');" />
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.bunrui1_cd, MainForm.bunrui1_na);" />
			</td>
			<th>大分類</th>
			<td nowrap>
				<input type="text" name="bunrui2_cd" value="<%= HTMLUtil.toText(nohinItiranStatus.getBunrui2Cd()) %>" size="8" maxlength="<%=bunrui2CdLen %>" style="ime-mode:disabled; width: 52px;" />
				<input type="text" name="bunrui2_na" value="<%= HTMLUtil.toText(nohinItiranStatus.getBunrui2Na()) %>" id="no_input_text" tabindex="-1" readonly style="width: 110px;"/>
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_DptLineClass('','','MainForm.bunrui2_cd','MainForm.bunrui2_na','MainForm.bunrui5_cd','MainForm.bunrui5_na','2',MainForm.bunrui1_cd.value , MainForm.bunrui2_cd.value );" />
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.bunrui2_cd, MainForm.bunrui2_na);" />
			</td>
		</tr>
		<tr>
			<th>小分類</th>
			<td nowrap>
				<input type="text" name="bunrui5_cd" value="<%= HTMLUtil.toText(nohinItiranStatus.getBunrui5Cd()) %>" size="8" maxlength="<%=bunrui5CdLen %>" style="ime-mode:disabled; width: 66px;" />
				<input type="text" name="bunrui5_na" value="<%= HTMLUtil.toText(nohinItiranStatus.getBunrui5Na()) %>" id="no_input_text" tabindex="-1" readonly style="width: 110px;"/>
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_DptLineClass('','','','','MainForm.bunrui5_cd','MainForm.bunrui5_na','3',MainForm.bunrui1_cd.value , MainForm.bunrui2_cd.value );" />
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.bunrui5_cd, MainForm.bunrui5_na);" />
			</td>
			<th nowrap>納品区分</th>
			<td nowrap align="left" style="height: 22px;">
				<select name="buturyu_kb" onChange="document.MainForm.ModifiedCondition.value='1';" >
					<option value=""></option>
					<%
					 if( maButuryuBh.getMaxRows() > 0 ) {
					   for( Iterator ite = maButuryuBh.getBeanIterator(); ite.hasNext(); ) {
							 MaButuryuBean bean = (MaButuryuBean)ite.next();
					%>
					 	<option value="<%= HTMLUtil.toText(bean.getButuryuKb()) %>" <%= nohinItiranStatus.getButuryuKb().trim().equals(bean.getButuryuKb().trim()) ?  " selected" : "" %>><%= HTMLUtil.toLabel(bean.getButuryuNm()) %></option>
					<%	 }	%>
					<% }	%>
				</select>
			</td>
		</tr>
		<tr>
			<th>処理状況</th>
			<td nowrap>
				<label><input type="radio" name="syori_jokyo" value="1" onClick="changeModifiedCondition();" <%= HTMLUtil.toRadioCheck(nohinItiranStatus.getSyoriJokyo().getCode(), SyoriJokyoDictionary.SUBETE.getCode()) %> /><span><%= SyoriJokyoDictionary.SUBETE.toString() %></span></label>
				<label><input type="radio" name="syori_jokyo" value="2" onClick="changeModifiedCondition();" <%= HTMLUtil.toRadioCheck(nohinItiranStatus.getSyoriJokyo().getCode(), SyoriJokyoDictionary.MIKAKUTEI.getCode()) %> /><span><%= SyoriJokyoDictionary.MIKAKUTEI.toString() %></span></label>
				<label><input type="radio" name="syori_jokyo" value="3" onClick="changeModifiedCondition();" <%= HTMLUtil.toRadioCheck(nohinItiranStatus.getSyoriJokyo().getCode(), SyoriJokyoDictionary.KAKUTEI.getCode()) %> /><span><%= SyoriJokyoDictionary.KAKUTEI.toString() %></span></label>
			</td>
			<th nowrap>便</th>
			<td valign="top" align="left" style="height: 22px;">
				<input type="text" name="bin_kb" value="<%= HTMLUtil.toText(nohinItiranStatus.getBinKb()) %>" size="1" maxlength="1" style="width: 17px;">
			</td>
		</tr>
		<tr>
			<th rowspan = "2">表示形式</th>
			<td nowrap rowspan = "2" style="height: 20px;">
				<label><input type="radio" name="hyoji_kb"  value="1" onClick="changeModifiedCondition();" <%= HTMLUtil.toRadioCheck(nohinItiranStatus.getHyojiKb().getCode(), HyojiKbDictionary.SYOHIN.getCode()) %> /><span><%= HyojiKbDictionary.SYOHIN.toString() %></span></label>&emsp;&emsp;&emsp;&nbsp;&nbsp;
				<label><input type="radio" name="hyoji_kb"  value="2" onClick="changeModifiedCondition();" <%= HTMLUtil.toRadioCheck(nohinItiranStatus.getHyojiKb().getCode(), HyojiKbDictionary.DENPYO.getCode()) %>/><span><%= HyojiKbDictionary.DENPYO.toString() %></span></label>
			</td>
		</tr>

	</table>
			</td>
		</tr>
	</table>

	<br />

	<input type="button" value="&emsp;検&emsp;索&emsp;" class="btn" onClick="searchCommand();" style="width: 97px;" />
	<input type="button" value="&emsp;ＣＳＶ&emsp;" class="btn" onClick="csvDownload();" style="width: 82px;" />
	<input type="button" value="&emsp;戻&emsp;る&emsp;" class="btn" onClick="isModifiedTran('nohinItiranExit');" style="width: 92px;" /><br />

	<br />

	<jsp:include page="InfoStringMdWare.jsp" />

	<br />


<%	if( nohinItiranBh.getMaxRows() > 0 ) {	%>

	<table border="0" cellspacing="0" cellpadding="2" style="height: 25px;" >
		<tr>
			<td class="size12" height="14" align="left">選択チェックボックスを
				<input type="button" name="btn_all" value="&emsp;ページ内全て選択&emsp;" class="select_btn" onclick ="setCheckData()"/>&emsp;
				<input type="button" name="btn_none" value="ページ内全て選択解除" class="select_btn" onclick="notSetCheckData()"/>&emsp;
				<input type="button" name="mikaku_all" value="ページ内未確定&emsp;選択" class="select_btn" onclick ="setMikakuCheckData()"/>&emsp;
			</td>
		</tr>
	</table>

<%--	商品別 START	--%>
<%		if( nohinItiranStatus.getHyojiKb().equals(HyojiKbDictionary.SYOHIN) ) {	%>
<table>
<tr align = "center">
<td>
	<div align="left" style="overflow-x:hidden; width: 980; height: 30px;">
	<table border="0" cellspacing="1" cellpadding="0" class="data" style="table-layout:fixed">
		<tr>
			<th width="25" align="center">選択</th>
			<th width="48" align="center">納品</th>
			<th width="104" align="center">商品コード</th>
			<th width="127" align="center">商品名</th>
			<th width="34" align="center">発注<br />単位</th>
			<th width="40" align="center">発注数</th>
			<th width="50" align="center">発注<br />数量</th>
			<th width="40" align="center">納品数</th>
			<th width="50" align="center">納品<br />数量</th>
			<th width="40" align="center">登録<br />数</th>
			<th width="50" align="center">登録<br />数量</th>
			<th width="58" align="center">原単価</th>
			<th width="52" align="center">売単価</th>
			<th width="34" align="center">値入率</th>
			<th width="47" align="center">産地</th>
			<th width="39" align="center">等級</th>
			<th width="39" align="center">規格</th>
			<th width="49" align="center">納品<br />区分</th>
			<th width="26" align="center">便</th>
			<th width="16"> </th>
		</tr>
	</table>
	</div>

	<div  align="left" style="overflow:scroll;overflow-x:hidden; width: 980; height: 170px;">
	<table border="0" cellspacing="1" cellpadding="0" class="data" >
<%
			// 明細表示
			int i = 0;
			for( Iterator ite = nohinItiranBh.getBeanIterator(); ite.hasNext(); i++ ) {
				NohinItiranSyohinBean bean = (NohinItiranSyohinBean)ite.next();
%>

		<tr>
			<%-- 選択 --%>
			<td width="25"><input type="checkbox" name="sentaku<%= i %>" value="<%= i %>" /></td>
			<%-- 納品 --%>
			<td width="48" align="center">
				<input type="hidden" name="kakutei_kb<%= i %>" value="<%= HTMLUtil.toText(bean.getKakuteiKb()) %>" />
				<input type="text" size="6" styleColor = "" value="<%=KakuteiKb.getStatus(bean.getKakuteiKb()).toString()%>" style="border-width: 0px; width: 40px;" tabindex="-1" readonly />
			</td>
			<%-- 商品コード --%>
			<td  width="100" class="string_label">
				<a href="#" onClick="nohinTorokuSyohinCommand(<%= i %>);"><%= HTMLUtil.toLabel(bean.getSyohinCd()) %></a>
			</td>

			<%-- 商品名 --%>
			<td styleColor = "" width="123" class="string_label">
				<input type="text" size="22" styleColor = "" value="<%=bean.getSyohinNa()%>" style="border-width: 0px; width: 120px;" tabindex="-1" readonly />
			</td>

			<%-- 発注単位名 --%>
			<td styleColor = "" width="30" class="string_label">
				<input type="text" size="3" styleColor = "" value="<%=bean.getHachuTaniNa()%>" style="border-width: 0px; width: 25px;" tabindex="-1" readonly />
			</td>

			<%-- 発注数 --%>
			<td styleColor = "" width="36" class="numeric_label">
				<input type="text" size="4" styleColor = "" value="<%= HTMLUtil.toLabel(bean.getHachuQtString(), "#,##0") %>" style="border-width: 0px; width: 30px;" class="numeric" tabindex="-1" readonly />
			</td>

			<%-- 発注数量 --%>
			<td styleColor = "" width="46" class="numeric_label">
				<input type="text" size="6" styleColor = "" value="<%= HTMLUtil.toLabel(bean.getHachuSuryoQtString(), "#,##0.##") %>" style="border-width: 0px; width: 40px;" class="numeric" tabindex="-1" readonly />
			</td>

			<%-- 納品数 --%>
			<td styleColor = "" width="36" class="numeric_label">
				<input type="text" size="4" styleColor = "" value="<%= HTMLUtil.toLabel(bean.getNohinQtString(), "#,##0") %>" style="border-width: 0px; width: 30px;" class="numeric" tabindex="-1" readonly />
			</td>

			<%-- 納品数量 --%>
			<td styleColor = "" width="46" class="numeric_label">
				<input type="text" size="6" styleColor = "" value="<%= HTMLUtil.toLabel(bean.getNohinSuryoQtString(), "#,##0.##") %>" style="border-width: 0px; width: 40px;" class="numeric" tabindex="-1" readonly />
			</td>

			<%-- 登録数 --%>
			<td styleColor = "" width="36" class="numeric_label">
				<input type="text" size="4" styleColor = "" value="<%= HTMLUtil.toLabel(bean.getTeiNohinQtString(), "#,##0") %>" style="border-width: 0px; width: 30px;" class="numeric" tabindex="-1" readonly />
			</td>

			<%-- 登録数量 --%>
			<td styleColor = "" width="46" class="numeric_label">
				<input type="text" size="6" styleColor = "" value="<%= HTMLUtil.toLabel(bean.getTeiNohinSuryoQtString(), "#,##0.##") %>" style="border-width: 0px; width: 40px;" class="numeric" tabindex="-1" readonly />
			</td>

			<%-- 原単価 --%>
			<td styleColor = "" width="54" class="numeric_label">
				<input type="text" size="8" styleColor = "" value="<%= HTMLUtil.toLabel(bean.getTeiGentankaVlString()) %>" style="border-width: 0px; width: 50px;" class="numeric" tabindex="-1" readonly />
			</td>

			<%-- 売単価 --%>
			<td styleColor = "" width="48" class="numeric_label">
				<input type="text" size="7" styleColor = "" value="<%= HTMLUtil.toLabel(bean.getTeiBaitankaVlString()) %>" style="border-width: 0px; width: 45px;" class="numeric" tabindex="-1" readonly />
			</td>


			<%-- 値入率 --%>
			<td class="numeric_label" width="30"><%= HTMLUtil.toLabel(bean.getNeiriRitu(), "#,##0.0") %></td>


			<%-- 産地名称 --%>
			<td class="string_label" width="43"  style="<%=bean.getSantiNaBgColor()%>" >
				<input type="text" value="<%= HTMLUtil.toText(bean.getSantiNa()) %>" size="5" style="border-width: 0px; width: 35px; <%=bean.getSantiNaBgColor()%>" tabindex="-1" readonly />
			</td>

			<%-- 等階級名称 --%>
			<td class="string_label" width="35" style="<%=bean.getTokaikyuNaBgColor()%>">
				<input type="text" value="<%= HTMLUtil.toText(bean.getTokaikyuNa()) %>" size="4" style="border-width: 0px; width: 30px; <%=bean.getTokaikyuNaBgColor()%>" tabindex="-1" readonly />
			</td>

			<%-- 規格名称 --%>
			<td class="string_label" width="35" style="<%=bean.getKikakuNaBgColor()%> ">
				<input type="text" value="<%= HTMLUtil.toText(bean.getKikakuNa()) %>" size="4" style="border-width: 0px; width: 30px; <%=bean.getKikakuNaBgColor()%>" tabindex="-1" readonly />
			</td>

			<%-- 物流区分 --%>
			<td styleColor = "" width="45" class="string_label">
				<input type="text" size="6" styleColor = "" value="<%=bean.getButuryuSn()%>" style="border-width: 0px; width: 40px;" tabindex="-1" readonly />
			</td>

			<%-- 便 --%>
			<td styleColor = "" width="21" align="right" class="numeric_label">
				<input type="text" size="2" styleColor = "" value="<%= HTMLUtil.toLabel(bean.getBinKb()) %>"  style="border-width: 0px; width: 20px;" class="numeric" tabindex="-1" readonly />
			</td>

		</tr>
<%			}	%>
	</table>
	</div>
</td>
</tr>
</table>
<%--	商品別　END	--%>

<%--	伝票別 START	--%>
<%		} else if( nohinItiranStatus.getHyojiKb().equals(HyojiKbDictionary.DENPYO) ){	%>
<table>
<tr align = "left">
<td>
	<div align="left" style="overflow-x:hidden; width: 995; height: 30px;">
	<table border="0" cellspacing="1" cellpadding="0" class="data">
		<tr>
			<th width="25" align="center">選択</th>
			<th width="52" align="center">納品</th>
			<th width="78" align="center">伝票番号</th>
			<th width="80" align="center">店舗コード</th>
			<th width="204" align="center">店舗名</th>
			<th width="78" align="center">納品区分</th>
			<th width="30" align="center">便</th>
			<th width="100" align="center">発注日</th>
			<th width="15" align="center"></th>
		</tr>
	</table>
	</div>
	<div  align="left" style="overflow:scroll;overflow-x:hidden; width: 995; height: 170px;">
	<table border="0" cellspacing="1" cellpadding="0" class="data" style="980" >
<%
			// 明細表示
			int i = 0;
			for( Iterator ite = nohinItiranBh.getBeanList().iterator(); ite.hasNext(); i++ ) {
				NohinItiranDenpyoBean bean = (NohinItiranDenpyoBean)ite.next();

%>
		<tr>
			<%-- 選択 --%>
			<td width="25" <%= bean.isTokubai() == true ? " style=\"background-color: #FFFFAA;\"" : "" %> align="center">
				<input type="checkbox" name="sentaku<%= i %>" value="<%= i %>" />
			</td>

			<%-- 納品 --%>
			<td width="52" <%= bean.isTokubai() == true ? " style=\"background-color: #FFFFAA;\"" : "" %> align="center">
				<input type="hidden" name="kakutei_kb<%= i %>" value="<%= HTMLUtil.toText(bean.getKakuteiKb()) %>" />
				<input type="text" size="7" value="<%= HTMLUtil.toLabel(KakuteiKb.getStatus(bean.getKakuteiKb()).toString()) %>" style="border-width: 0px; width: 45px; <%= bean.isTokubai() == true ? "background-color: #FFFFAA; " : "" %>"  tabindex="-1" readonly />
			</td>

			<%-- 伝票番号 --%>
			<td width="74" <%= bean.isTokubai() == true ? " style=\"background-color: #FFFFAA;\"" : "" %> class="string_label">
				<a href="#" onClick="nohinTorokuDenpyoCommand(<%= i %>);"><%= HTMLUtil.toLabel(bean.getDenpyoNb()) %></a>
			</td>

			<%-- 店舗コード --%>
			<td width="76" <%= bean.isTokubai() == true ? " style=\"background-color: #FFFFAA;\"" : "" %> class="string_label">
				<input type="text" size="12" value="<%=bean.getTenpoCd()%>" style="border-width: 0px; width: 70px; <%= bean.isTokubai() == true ? "background-color: #FFFFAA; " : "" %>"  tabindex="-1" readonly />
			</td>

			<%-- 店舗名 --%>
			<td width="200" <%= bean.isTokubai() == true ? " style=\"background-color: #FFFFAA;\"" : "" %> class="string_label">
				<input type="text" size="37" value="<%=bean.getTenpoNa()%>" style="border-width: 0px; width: 195px; <%= bean.isTokubai() == true ? "background-color: #FFFFAA; " : "" %>"  tabindex="-1" readonly />
			</td>

			<%-- 納品区分 --%>
			<td width="74" <%= bean.isTokubai() == true ? " style=\"background-color: #FFFFAA;\"" : "" %> class="string_label">
				<input type="text" size="12" value="<%=bean.getButuryuSn()%>" style="border-width: 0px; width: 70px; <%= bean.isTokubai() == true ? "background-color: #FFFFAA; " : "" %>"  tabindex="-1" readonly />
			</td>

			<%-- 便 --%>
			<td width="26" <%= bean.isTokubai() == true ? " style=\"background-color: #FFFFAA;\"" : "" %> class="numeric_label">
				<input type="text" size="1" value="<%=bean.getBinKb()%>" style="border-width: 0px; width: 15px; <%= bean.isTokubai() == true ? "background-color: #FFFFAA; " : "" %> " class="numeric"  tabindex="-1" readonly />
			</td>

			<%-- 発注日 --%>
			<td width="100" <%= bean.isTokubai() == true ? " style=\"background-color: #FFFFAA;\"" : "" %> align="center">
				<input type="text" size="12" value="<%= HTMLUtil.toDate(bean.getHachuDt(), "yyyy/MM/dd") %>" style="border-width: 0px; width: 70px; <%= bean.isTokubai() == true ? "background-color: #FFFFAA; " : "" %>"  tabindex="-1" readonly />
			</td>
		</tr>
<%			}	%>
	</table>
	</div>
</td>
</tr>
</table>
<%--	伝票別　END	--%>
<%		}	%>

	<div align="center">
		<table style="height: 20px;">
			<tr>
				<td align="left">
		<font color="#0000FF">
			<b>納品</b>：未確定＝未確定&nbsp;確定済＝確定済&nbsp;出力済＝納品リスト出力済<br>
		</font>
				</td>
			</tr>
		</table>
	</div>

<%		if( HyojiKbDictionary.DENPYO.equals(nohinItiranStatus.getHyojiKb()) ) { 	%>
<table align="left" class="addPadding" style="line-height: 14px;">
		<tr>
			<td nowrap width="100"></td>
			<td nowrap width="50" style="background-color: #FFFFAA;"></td>
			<td nowrap >・・・「特売」</td>
		</tr>
	</table><br>
<%		}	%>

	<br />
	<table border="0" cellspacing="0" cellpadding="0" style="height: 35px;">
		<td align="center">
			<div class="navi_data">
<%
		String first = "";
		String prev = "";
		String next = "";
		String last = "";

		if( nohinItiranBh.getCurrentPageNumber() == 1 ) {
			first = "disabled";
			prev = "disabled";
		}
		if( nohinItiranBh.getCurrentPageNumber() == nohinItiranBh.getLastPageNumber() || nohinItiranBh.getLastPageNumber() == 0) {
			next = "disabled";
			last = "disabled";
		}
%>
				<input type="button" value="先頭" <%= first %> onClick="changePage('first');"  style="width: 38px;"/>
				<input type="button" value="前の<%= NohinItiranStatus.ROWS_IN_PAGE %>件" <%= prev %> onClick="changePage('prev');" style="width: 76px;" />
				<input type="button" value="次の<%= NohinItiranStatus.ROWS_IN_PAGE %>件" <%= next %> onClick="changePage('next');" style="width: 76px;"  />
				<input type="button" value="最終" <%= last %> onClick="changePage('last');" style="width: 38px;" />
			</div>
			<span class="size12">(<%= nohinItiranBh.getMaxRows() %>件中<%= nohinItiranBh.getStartRowInPage() %>～<%= nohinItiranBh.getEndRowInPage() %>件目）</span>
		</td>
	</table>

	<br />
	<table class="addPadding">
	<tr>
		<td width="250">
		</td >
		<td align="left" >
		<input type="button" name="" value="&emsp;確&emsp;&emsp;定&emsp;" class="btn" onClick="decideCommand();" style="width: 116px;"/>
		</td>
		<td align="left">
		<font color="#0000FF">※チェックした明細行を確定します。</font>
		</td>
	</tr>
	<tr>
		<td >
		</td >
		<td align="left">
		<input type="button" name="" value="&emsp;一括確定&emsp;" class="btn" onClick="allDecideCommand();" style="width: 116px;"/>
		</td>
		<td align="left">
		<font color="#0000FF">※検索条件に該当するデータを一括確定します。</font>
		</td>
	</tr>
	</table>

	<br />
	<br />

	<table border="0" cellspacing="0" cellpadding="0" class="kensaku" width="300">
		<tr>
			<th style="width:100px;">リスト出力対象</th>
			<td width="200px" align="center">
				<label><input type="radio" name="list_target" value="0" <%= HTMLUtil.toRadioCheck(nohinItiranStatus.getListTarget().getCode(), ListTarget.ALL.getCode()) %> /><%= ListTarget.ALL.toString() %>&emsp;</label>
				<label><input type="radio" name="list_target" value="1" <%= HTMLUtil.toRadioCheck(nohinItiranStatus.getListTarget().getCode(), ListTarget.MI_ONLY.getCode()) %> /><%= ListTarget.MI_ONLY.toString() %></label>
			</td>
		</tr>
		<tr>
			<td colspan="2">
				<div align="center">
					<input type="button" name="" value="納品リスト出力" class="btn" onClick="deliveryListCommand();" style="width: 118px;"/>
				</div>
			</td>
		</tr>
	</table>

<%	}	%>

</form>
<!-- Body END -->
<!---- システム共通フッター START ---->
	<table border="2" cellspacing="0" cellpadding="0" class="kensaku tableNote" width="600">
		<tr>
			<td>
				<br />
				&emsp;<font color="#0000FF"><b>※「納品リスト」について</b><br />
				&emsp;未出力のリストのみ出力したい場合は、リスト出力対象の「未出力のみ」を選択し、<br />
				&emsp;&emsp;各リストボタンを押してください。</font>
				<br />
				<br />
			</td>
			<td style="width: 3px; border-width: 0px;"></td>
		</tr>
		<tr style="height: 1px;"></tr>
	</table>
<!---- システム共通フッター END ---->
</table>
</body>