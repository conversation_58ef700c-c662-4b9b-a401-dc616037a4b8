<body bgcolor="#FFFFFF" text="#000000" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0">
<table width="100%" border="0" cellspacing="0" cellpadding="0" height="100%">
<!------ システム共通ヘッダー  START ------>
	<jsp:include page="ptl000001_Header.jsp?PARAM=納品確定状況明細照会（SSN07002）"></jsp:include>
	<tr height="5"></tr>
<!------ システム共通ヘッダー  END ------>
<!------ システム共通メニューバー  START ------>
<!------ システム共通メニューバー  END ------>

	<tr>
		<td align="center" valign="top">

<!------ Body START ------>
<form name="MainForm" method="post" action="app">
	<jsp:include page="rbs00000_common.jsp" flush="true" />

	<%
	List li = DDCbeanHolder.getBeanList();
	Iterator iteHead = li.iterator();
	DeliveryDetailCheckBean ddcBean = new DeliveryDetailCheckBean();
	ddcBean = (DeliveryDetailCheckBean)iteHead.next();
	%>

	<table border="0" cellspacing="1" cellpadding="0" class="kensaku">
		<tr>
			<th>取引先</th>
			<td nowrap colspan="3" class="string_label">
				<input type="text" name="torihikisaki_cd" style="width: 55px;" size="9" value="<%= HTMLUtil.toText(ddcBean.getTorihikisakiCd()) %>" id="no_input_text" tabindex="-1" readonly />
				<input type="text" name="torihikisaki_na" style="width: 160px;" size="30" value="<%= HTMLUtil.toText(ddcBean.getTorihikisakiNa()) %>" id="no_input_text" tabindex="-1" readonly />
			</td>
			<th>伝票番号</th>
			<td nowrap class="string_label">
				<input type="text" name="denpyo_nb" style="width: 65px;" size="11" value="<%= HTMLUtil.toText(ddcBean.getDenpyoNb()) %>" id="no_input_text" tabindex="-1" readonly />
			</td>
			<th>店舗</th>
			<td nowrap class="string_label">
				<input type="text" name="tenpo_cd" value="<%= HTMLUtil.toText(ddcBean.getTenpoCd()) %>"size="6" id="no_input_text" tabindex="-1" style="ime-mode:disabled; width: 42px;" />
				<input type="text" name="tenpo_na" value="<%= HTMLUtil.toText(ddcBean.getTenpoNa()) %>" id="no_input_text" tabindex="-1" readonly style="width: 110px;" />
			</td>
			</td>
		</tr>
			<th>納品区分</th>
			<td nowrap class="string_label" width="100">
				<input type="text" size="13" style="width: 75px;" value="<%= HTMLUtil.toLabel(ddcBean.getButuryuNm()) %>" id="no_input_text" tabindex="-1" readonly />
			</td>
			<th>発注日</th>
			<td nowrap class="string_label" width="100">
				<input type="text" name="hachu_dt" size="13" style="width: 75px;" value="<%= HTMLUtil.toDate(ddcBean.getHachuDt(), "yyyy/MM/dd")  %>" id="no_input_text" tabindex="-1" readonly />
			</td>
			<th>納品予定日</th>
			<td nowrap class="string_label" width="100">
				<input type="text" name="nohin_dt" size="13" style="width: 75px;" value="<%= HTMLUtil.toDate(ddcBean.getNohinDt(), "yyyy/MM/dd")  %>" id="no_input_text" tabindex="-1" readonly />
			</td>
			<th>納品日</th>
			<td nowrap class="string_label" width="200">
				<input type="text" name="zitu_nou_dt" size="13" style="width: 75px;" value="<%= HTMLUtil.toDate(ddcBean.getZituNouDt(), "yyyy/MM/dd") %>" id="no_input_text" tabindex="-1" readonly />
			</td>
		</tr>
		<tr>
			<th nowrap>便</th>
			<td nowrap class="string_label" width="100">
				<input type="text" name="bin_nm" size="3" style="width: 25px;" value="<%= HTMLUtil.toText(ddcBean.getBinKb(), "#") %>"id="no_input_text" class="numeric" tabindex="-1" readonly />
			</td>
		</tr>
	</table>

	<br />

	<table width="50%" border="0" cellspacing="0" cellpadding="2" class="guide">
		<tr>
			<td><br>
			<div></div>
			</td>
		</tr>
	</table>

	<br />


<%	if( DDCbeanHolder.getMaxRows() > 0 ) {	%>

	<table>
		<tr>
			<td align="left" nowrap style="line-height: 14px; padding: 1px;">
			<font color="0000FF">※背景が黄色で表示されているデータは、お取引先様が納品確定した納品数、納品数量とセンターで確定した納品数、納品数量が異なるものです</font>
			</td>
		</tr>
  	</table>
	<table border="0" cellspacing="0" cellpadding="0" class="data">
		<tr align = "center">
			<td >
			<div align = "left" style="width: 977px;">
			<table border="0" cellspacing="1" cellpadding="0" class="data" style="line-height: 14px;">
				<tr>
					<th nowrap width="15"></th>
					<th nowrap width="210" align="center">
						<table border="0">
							<tr nowrap>
								<th nowrap align="left" width="100">商品コード</th>
								<th nowrap align="left" width="50">入数</th>
								<th nowrap align="left" width="50">発単</th>
							</tr>
							<tr nowrap>
								<th nowrap align="left" colspan="3">商品名</th>
							</tr>
						</table>
					</th>
					<th nowrap width="50" align="center">発注<br />数</th>
					<th nowrap width="70" align="center">発注<br />数量</th>
					<th nowrap width="50" align="center">納品<br />数</th>
					<th nowrap width="70" align="center">納品<br />数量</th>
					<th nowrap width="50" align="center">訂正後<br />納品数</th>
					<th nowrap width="70" align="center">訂正後<br />納品数量</th>
					<th nowrap width="80" align="center">原単価</th>
					<th nowrap width="70" align="center">売単価</th>
					<th nowrap width="70" align="center">産地</th>
					<th nowrap width="70" align="center">等級</th>
					<th nowrap width="70" align="center">規格</th>
					<th nowrap width="17" align="center"></th>
				</tr>
			</table>
			</div>
			</td>
		</tr>
		<tr align = "center">
			<td >
			<div align = "left" style="overflow:scroll;overflow-x:hidden; width: 977px;height: 300px;">
			<table border="0" cellspacing="1" cellpadding="0" class="data">

		<%
				// 明細表示
				int i = 0;
				for( Iterator ite = DDCbeanHolder.getBeanIterator(); ite.hasNext(); i++ ) {
					DeliveryDetailCheckBean bean = (DeliveryDetailCheckBean)ite.next();

					String styleColor = "";
					if(!bean.getNohinQtString().equals(bean.getTeiNohinQtString())){
						styleColor = "background-color: #FFFFAA;";
					}else if(!bean.getNohinSuryoQtString().equals(bean.getTeiNohinSuryoQtString())){
						styleColor = "background-color: #FFFFAA;";
					}

		%>
				<tr>
					<%-- データ伝票行番号 --%>
					<td nowrap width="11" class="numeric_label" style="<%=styleColor%>"><%= HTMLUtil.toLabel(bean.getDenpyogyoNbString()) %></td>
					<td nowrap width="210" style="<%=styleColor%>">
						<table>
							<tr>
								<%-- 商品コード --%>
								<td nowrap align="left" width="100" style="<%=styleColor%>" ><input type="text" size="17" value="<%= HTMLUtil.toLabel(bean.getHachuSyohinCd()) %>" style="border-width: 0px; width: 95px; <%=styleColor%>" tabindex="-1" readonly /></td>
								<%-- 入数 --%>
								<td nowrap align="left" width="50" style="<%=styleColor%>" ><input type="text" size="5" value="<%= HTMLUtil.toLabel(bean.getIrisuQtString(), "#,##0") %>" style="border-width: 0px;  width: 35px; <%=styleColor%>" class="numeric" tabindex="-1" readonly /></td>
								<%-- 発注単位名 --%>
								<td nowrap align="left" width="50" style="<%=styleColor%>" ><input type="text" size="6" value="<%= HTMLUtil.toLabel(bean.getHachuTaniNa()) %>" style="border-width: 0px;  width: 40px; <%=styleColor%>" tabindex="-1" readonly /></td>
							</tr>
							<tr>
								<%-- 商品名 --%>
								<td nowrap align="left" colspan="3" style="<%=styleColor%>" ><input type="text" size="38" value="<%= HTMLUtil.toLabel(bean.getSyohinNa()) %>" style="border-width: 0px;  width: 200px; <%=styleColor%>" tabindex="-1" readonly /></td>
							</tr>
						</table>
					</td>
					<%-- 発注数 --%>
					<td nowrap width ="50" align="center" style="<%=styleColor%>" ><input type="text" size="5" value="<%= HTMLUtil.toLabel(bean.getHachuQtString(), "#,##0") %>" style="border-width: 0px; width: 35px; <%=styleColor%>" class="numeric" tabindex="-1" readonly /></td>
					<%-- 発注数量 --%>
					<td nowrap width ="70" align="center" style="<%=styleColor%>" ><input type="text" size="10" value="<%= HTMLUtil.toLabel(bean.getHachuSuryoQtString(), "#,##0.##") %>" style="border-width: 0px; width: 60px; <%=styleColor%> " class="numeric" tabindex="-1" readonly /></td>
					<%-- 納品数 --%>
					<td nowrap width ="50" align="center" style="<%=styleColor%>" ><input type="text" size="5"  value="<%= HTMLUtil.toLabel(bean.getNohinQtString(), "#,##0") %>" style="border-width: 0px;  width: 35px; <%=styleColor%>" class="numeric" tabindex="-1" readonly /></td>
					<%-- 納品数量 --%>
					<td nowrap width ="70" align="center" style="<%=styleColor%>" ><input type="text" size="10" value="<%= HTMLUtil.toLabel(bean.getNohinSuryoQtString(), "#,##0.##") %>" style="border-width: 0px;  width: 60px; <%=styleColor%>" class="numeric" tabindex="-1" readonly /></td>
					<%-- 訂正後納品数 --%>
					<td nowrap width ="50" align="center" style="<%=styleColor%>" ><input type="text" size="5"  value="<%= HTMLUtil.toLabel(bean.getTeiNohinQtString(), "#,##0") %>" style="border-width: 0px;  width: 35px; <%=styleColor%>" class="numeric" tabindex="-1" readonly /></td>
					<%-- 訂正後納品数量 --%>
					<td nowrap width ="70" align="center" style="<%=styleColor%>" ><input type="text" size="10" value="<%= HTMLUtil.toLabel(bean.getTeiNohinSuryoQtString(), "#,##0.##") %>" style="border-width: 0px;  width: 60px; <%=styleColor%>" class="numeric" tabindex="-1" readonly /></td>
					<%-- 原単価 --%>
					<td nowrap width ="80" align="center" style="<%=styleColor%>" ><input type="text" size="12" value="<%= HTMLUtil.toText(bean.getTeiGentankaVl(), "0.00") %>" style="border-width: 0px;  width: 70px; <%=styleColor%>" class="numeric" tabindex="-1" readonly /></td>
					<%-- 売単価 --%>
					<td nowrap width ="70" align="center" style="<%=styleColor%>" ><input type="text" size="8"  value="<%= HTMLUtil.toText(bean.getTeiBaitankaVl(), "0") %>" style="border-width: 0px;  width: 50px; <%=styleColor%>" class="numeric" tabindex="-1" readonly /></td>
					<%-- 産地 --%>
					<td nowrap width ="70"align="center"  style="<%=styleColor%>" ><input type="text" size="11" value="<%= HTMLUtil.toLabel(bean.getTeiSantiNa()) %>" style="border-width: 0px;  width: 65px; <%=styleColor%>" tabindex="-1" readonly /></td>
					<%-- 等級 --%>
					<td nowrap width ="70"align="center"  style="<%=styleColor%>" ><input type="text" size="11" value="<%= HTMLUtil.toLabel(bean.getTeiTokaikyuNa()) %>" style="border-width: 0px;  width: 65px; <%=styleColor%>" tabindex="-1" readonly /></td>
					<%-- 規格 --%>
					<td nowrap width ="70" align="center" style="<%=styleColor%>" ><input type="text" size="11" value="<%= HTMLUtil.toLabel(bean.getTeiKikakuNa()) %>" style="border-width: 0px;  width: 65px; <%=styleColor%>" tabindex="-1" readonly /></td>
				</tr>
<%		}	%>
			</table>
			</div>
			</td>
		</tr>
		<tr>
			<td>
			<br>
			<table width="997" border="0" cellspacing="0" cellpadding="0">
				<tr>
					<td align="center">
						<table border="0" cellspacing="1" cellpadding="0" class="shori_info" style="line-height: 14px;">
						<tr>
							<th style="width: 95px;">原価金額合計</th>
							<td height="22" width="100" align="center">
								<input type="text" size="16" style="width: 90px;" value="<%= HTMLUtil.toText(genkabaikaHolder.getParameter("genkaVl"), "#,##0") %>" class="numeric" id="no_input_text" tabindex="-1" readonly />
							</td>
							<th style="width: 95px;">売価金額合計</th>
							<td height="22" width="100" align="center">
								<input type="text" size="16" style="width: 90px;" value="<%= HTMLUtil.toText(genkabaikaHolder.getParameter("baikaVl"), "#,##0") %>" class="numeric" id="no_input_text" tabindex="-1" readonly />
							</td>
							<th style="width: 95px;">訂正後<br>原価金額合計</th>
							<td height="22" width="100" align="center">
								<input type="text" size="16" style="width: 90px;" value="<%= HTMLUtil.toText(genkabaikaHolder.getParameter("teiGenkaVl"), "#,##0") %>" class="numeric" id="no_input_text" tabindex="-1" readonly />
							</td>
							<th style="width: 95px;">訂正後<br>売価金額合計</th>
							<td height="22" width="100" align="center">
								<input type="text" size="16" style="width: 90px;" value="<%= HTMLUtil.toText(genkabaikaHolder.getParameter("teiBaikaVl"), "#,##0") %>" class="numeric" id="no_input_text" tabindex="-1" readonly />
							</td>
						</tr>
						</table>
					</td>
				</tr>
			</table>
			</td>
		</tr>
	</table>
<%	}	%>

	<br />
	<br />

	<!------ ボタン ------>
	<table align="center"  border="0" cellspacing="0" cellpadding="0">
		<tr>
			<td align="left">
			<input type="button" value="&nbsp;納品確定状況照会&nbsp;" class="btn" onClick="javascript:goBack()" style="width: 167px;"/>
			</td>
			<td width="500">
			</td>
			<td align="right">
			</td>
		</tr>
	</table>



</form>
<!------ Body END ------>

		</td>
	</tr>
<!---- システム共通フッター START ---->
<!---- システム共通フッター END ---->
</table>
</body>