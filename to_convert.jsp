<body bgcolor="#FFFFFF" text="#000000" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" onLoad="setFocus();init();putOnLoadDisplay();outputList();">
<table width="100%" border="0" cellspacing="0" cellpadding="0" hight="100%">
<!------ システム共通ヘッダー START --------------------------------------------------------------------------------------------------------------------------------------------->
	<jsp:include page="ptl000001_Header.jsp?PARAM=納品一括処理（SSN04010）"></jsp:include><!------ システム共通ヘッダー END   --------------------------------------------------------------------------------------------------------------------------------------------->
	<tr height="5"></tr>
<!------ システム共通メニューバー  START ---------------------------------------------------------------------------------------------------------------------------------------->
<!------ システム共通メニューバー  END ------------------------------------------------------------------------------------------------------------------------------------------>
<!------ Body START ------------------------------------------------------------------------------------------------------------------------------------------------------------->
<form name="MainForm" method="post" action="app">
<jsp:include page="rbs00000_common.jsp" flush="true" />
<input type="hidden" name="Modified" value="">
<input type="hidden" name="ModifiedCondition" value="">
<input type="hidden" name="outPutFlg" value="<%= outPutFlg %>" />

	<tr>
		<td align="center" valign="top">

<!------ 検索条件部 START ------------------------------------------------------------------------------------------------------------------------------------------------------->

	<table border="0" cellspacing="1" cellpadding="0" class="kensaku" width="800" >
		<tr >
			<th>*取引先</th>
			<td nowrap align="left" style="width: 283px;">
				<%
				//取引先権限振分
				if(RoleUtil.isTorihikisakiFurumai(role)){
				%>
						<input type="text" name="torihikisaki_cd" value="<%= HTMLUtil.toText(nohinAllStatus.getTorihikisakiCd()) %>" style="width: 50px;" size="8" maxlength="<%=torihikisakiCdLen %>" id="no_input_text" tabindex="-1"readOnly>
						<input type="text" name="torihikisaki_na" value="<%= HTMLUtil.toText(nohinAllStatus.getTorihikisakiNa()) %>" style="width: 210px;" size="40" id="no_input_text" tabindex="-1" readOnly>
				<%}else{%>
						<input type="text" name="torihikisaki_cd" size="8" maxlength="<%=torihikisakiCdLen %>" value="<%= HTMLUtil.toText(nohinAllStatus.getTorihikisakiCd()) %>" style="ime-mode:disabled; width: 52px;" />
						<input type="text" name="torihikisaki_na"  value="<%= HTMLUtil.toText(nohinAllStatus.getTorihikisakiNa()) %>" id="no_input_text" tabindex="-1" readonly style="width: 110px;" />
						<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_siireSel('MainForm.torihikisaki_cd','MainForm.torihikisaki_na');" />
						<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.torihikisaki_cd, MainForm.torihikisaki_na);" />
				<%}%>


			</td>
			<th>*納品日</th>
			<td  nowrap colspan="" nowrap style="width: 324px;">
				<input type="text" name="nohin_dt_from" value="<%= HTMLUtil.toText(nohinAllStatus.getNohinDtFrom()) %>" size="10" maxlength="8" style="ime-mode:disabled; width: 62px;" />
				<img src="./images/calendar.gif" width="18" height="18" align="absmiddle" alt="日付選択" onClick="callCalendar(MainForm, MainForm.nohin_dt_from);" />(自)&nbsp;～
				<input type="text" name="nohin_dt_to" value="<%= HTMLUtil.toText(nohinAllStatus.getNohinDtTo()) %>" size="10" maxlength="8" style="ime-mode:disabled; width: 62px;" />
				<img src="./images/calendar.gif" width="18" height="18" align="absmiddle" alt="日付選択" onClick="callCalendar(MainForm, MainForm.nohin_dt_to);" />(至)
				<small>（YYYYMMDD）</small>
			</td>
		</tr>
		<tr>
			<th>*部門</th>
			<td nowrap>
				<input type="text" name="bunrui1_cd" value="<%= HTMLUtil.toText(nohinAllStatus.getBunrui1Cd()) %>" size="8" maxlength="<%=bunrui1CdLen %>" style="ime-mode:disabled; width: 52px;" />
				<input type="text" name="bunrui1_na" value="<%= HTMLUtil.toText(nohinAllStatus.getBunrui1Na()) %>" id="no_input_text" tabindex="-1" readonly style="width: 110px;" />
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_DptLineClass('MainForm.bunrui1_cd','MainForm.bunrui1_na','MainForm.bunrui2_cd','MainForm.bunrui2_na','MainForm.bunrui5_cd','MainForm.bunrui5_na','1',MainForm.bunrui1_cd.value , '');" />
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.bunrui1_cd, MainForm.bunrui1_na);" />
			</td>
			<th>便</th>
			<td nowrap>
				<input type="text" name="bin_nm" value="<%= HTMLUtil.toText(nohinAllStatus.getBinNm()) %>" size="1" maxlength="1" style="ime-mode:disabled; width: 17px;" />
			</td>
		</tr>
		<tr>
			<th>店舗</th>
			<td nowrap>
				<input type="text" name="tenpo_cd" value="<%= HTMLUtil.toText(nohinAllStatus.getTenpoCd()) %>" size="8" maxlength="<%=tenpoCdLen %>" style="ime-mode:disabled; width: 52px;" />
				<input type="text" name="tenpo_na" value="<%= HTMLUtil.toText(nohinAllStatus.getTenpoNa()) %>" id="no_input_text" tabindex="-1" readonly style="width: 110px;" />
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_tenpoSel('MainForm.tenpo_cd','MainForm.tenpo_na','');"/>
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.tenpo_cd, MainForm.tenpo_na);" />
			</td>
			<th>納品区分</th>
			<td>

				<select name="buturyu_kb" onChange="changeModifiedCondition();">
					<option value=""></option><%
	if( maButuryuBh.getMaxRows() > 0 ) {
		for( Iterator ite = maButuryuBh.getBeanIterator(); ite.hasNext(); ) {
			MaButuryuBean bean = (MaButuryuBean)ite.next();
%>
					<option value="<%= HTMLUtil.toText(bean.getButuryuKb().trim()) %>"<%= nohinAllStatus.getButuryuKb().trim().equals(bean.getButuryuKb().trim()) ? " selected" : "" %>><%= HTMLUtil.toLabel(bean.getButuryuNm()) %></option>
<%	}	%>
<%
	}
%>
				</select>
			</td>
		</tr>
		<tr>
			<th style="width:80px">*対象データ</th>
			<td>
				<label><input type="checkbox" name="data_soba" value="" <%= nohinAllStatus.isDataSoba() ? "checked":"" %> onClick="changeModifiedCondition();"  /><span>相場</span></label>&emsp;&emsp;
				<label><input type="checkbox" name="data_hisoba_tokubai" value="" <%= nohinAllStatus.isDataHisobaTokubai() ? "checked":"" %> onClick="changeModifiedCondition();"  /><span>非相場/特売</span></label>&emsp;&emsp;
				<label><input type="checkbox" name="data_kinkyu" value="" <%= nohinAllStatus.isDataKinkyu() ? "checked":"" %> onClick="changeModifiedCondition();"  /><span>緊急</span></label>&emsp;&emsp;
			</td>
			<th style="width:80px">経由センタ</th>
			<td nowrap>
		        <%
				//センター権限振分
				if(RoleUtil.isCenterFurumai(role)){
				%>
					<input type="text" name="center_cd" value="<%= HTMLUtil.toText(nohinAllStatus.getCenterCd()) %>" size="8" maxlength="<%=centerCdLen %>" id="no_input_text" tabindex="-1"readOnly>
					<input type="text" name="center_na" value="<%= HTMLUtil.toText(nohinAllStatus.getCenterNa()) %>" id="no_input_text" tabindex="-1" readonly />
			    <%}else{%>
					<input type="text" name="center_cd" value="<%= HTMLUtil.toText(nohinAllStatus.getCenterCd()) %>" size="8" maxlength="<%=centerCdLen %>" style="ime-mode:disabled; width: 52px;" />
					<input type="text" name="center_na" value="<%= HTMLUtil.toText(nohinAllStatus.getCenterNa()) %>" id="no_input_text" tabindex="-1" readonly style="width: 110px;" />
					<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_centerSel('MainForm.center_cd','MainForm.center_na');"/>
					<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.center_cd, MainForm.center_na);" />
				<%}%>
			</td>
		</tr>
	</table>
	<table width="800" border="0" cellspacing="0" cellpadding="2" class="guide">
		<tr>
			<td>
			<div align="left" style="width: 800px;">
				<font color = '#0000FF' style="font-weight: 400;" >※必要のない対象データはチェックをはずしてください</font>
			</div>
			</td>
		</tr>
	</table>
	</div>

	<br />
	<input type="button" value="&emsp;検&emsp;索&emsp;" style="width: 97px;" class="btn" onClick="searchCommand();" />
	<input type="button" value="&emsp;戻&emsp;る&emsp;" style="width: 92px;" class="btn" onClick="isModifiedTran('jutyuSubMenu');" /><br />

	<br />
<!------ 検索条件部 END   ------------------------------------------------------------------------------------------------------------------------------------------------------->
<!------ メッセージ表示部 START ------------------------------------------------------------------------------------------------------------------------------------------------->
	<jsp:include page="InfoStringMdWare.jsp" />

	<br />
<!------ メッセージ表示部 END    ------------------------------------------------------------------------------------------------------------------------------------------------>
<!------ 検索結果  START  ------------------------------------------------------------------------------------------------------------------------------------------------------->
<%	if( nohinAllStatus.hasSearchResult()) {	%>
<table>
<tr>
<td>
	<div align="left" style="width: 1980;">
	<table border="0" cellspacing="1" cellpadding="0" class="data" style="table-layout:fixed; line-height: 14px;">
		<tr>
			<th width="100" align="center">対象データ</th>
 			<th width="50"  align="center">納品</th>
 			<th width="80"  align="center">納品日</th>
 			<th width="70"  align="center">伝票枚数</th>
			<th width="90"  align="center">原価金額<br>合計</th>
			<th width="100" align="center">売価金額<br>合計</th>
			<th width="17"></th>
		</tr>
	</table>
	</div>

	<div  align="left" style="overflow:scroll;overflow-x:hidden; width: 1980; height: 253px;">
	<table border="0" cellspacing="1" cellpadding="0" class="data" >
	<%
			// 明細表示
	    	Iterator itr = nohinAllStatus.getBeanList().iterator();
	    	while( itr.hasNext() ) {
				NohinAllBean bean = (NohinAllBean)itr.next();
	%>
		<tr height="20">
			<td width="100" align="center"><%= HTMLUtil.toLabel(TaisyoData.getStatus(bean.getTaisyoData()).toString()) %></td>
			<td width="50" align="center"><%= HTMLUtil.toLabel(NohinSyoriKb.getStatus(bean.getNohinSyoriKb()).toString()) %></td>
			<td width="80" align="center"><%= HTMLUtil.toDate(bean.getNohinDt(), "yyyy/MM/dd") %></td>
			<td width="66" class="numeric_label"><%= HTMLUtil.toLabel(bean.getDenpyoQtString(),"#,##0") %></td>
			<td width="86" class="numeric_label"><%= HTMLUtil.toLabel(bean.getGenkaKeiVlString(),"#,##0") %></td>
			<td width="96" class="numeric_label"><%= HTMLUtil.toLabel(bean.getBaikaKeiVlString(),"#,##0") %></td>
		</tr>
<%		}	%>
	</table>
	</div>
</td>
</tr>
</table>

	<font color="#0000FF">
	<b>納品</b>：未確定＝受注データ未確定&emsp;確定済＝受注データ確定済&emsp;出力済＝納品リスト出力済
	</font>
	<br />
	<br />

	<table align="center" >
	<tr>
		<td width="260">
		</td >
		<td align="left">
		<input type="button" name="" value="&emsp;一括確定&emsp;" class="btn" onClick="decideCommand();" style="width: 116px;"/>
		</td>
		<td align="left">
		<font color="#0000FF">※納品が「未確定」のデータを一括確定します。</font>
		</td>
	</tr>
	</table>
	<br />

	<table border="0" cellspacing="0" cellpadding="0" class="kensaku" width="300">
		<tr>
			<th style="width:100px;">リスト出力対象</th>
			<td width="200px" align="center">
				<label><input type="radio" name="list_target" value="0" <%= HTMLUtil.toRadioCheck(nohinAllStatus.getListTarget().getCode(), ListTarget.ALL.getCode()) %> /><span><%= ListTarget.ALL.toString() %></span></label>&emsp;
				<label><input type="radio" name="list_target" value="1" <%= HTMLUtil.toRadioCheck(nohinAllStatus.getListTarget().getCode(), ListTarget.MI_ONLY.getCode()) %> /><span><%= ListTarget.MI_ONLY.toString() %></span></label>
			</td>
		<tr>
		</tr>
			<td colspan="2" style="height: 30px;">
				<div align="center">
					<input type="button" name="" value="納品リスト出力" class="btn" onClick="listCommand();" style="width: 118px;"/>
				</div>
			</td>
		</tr>
	</table>
<%	} %>
<!------ 検索結果  END  --------------------------------------------------------------------------------------------------------------------------------------------------------->
</form>
<!------ Body End --------------------------------------------------------------------------------------------------------------------------------------------------------------->
<!---- システム共通フッター START ----------------------------------------------------------------------------------------------------------------------------------------------->
	<table border="2" cellspacing="0" cellpadding="0" class="kensaku tableNote" width="600">
		<tr>
			<td>
				<br />
				&emsp;<font color="#0000FF"><b>※検索について</b><br/>
				&emsp;　納品日（自）のみ指定されている場合は、指定された納品日のみが対象となります。<br />
				&emsp;&emsp;</font>
				<br />
				&emsp;<font color="#0000FF"><b>※「納品リスト」について</b><br />
				&emsp;1.納品日は(自)に入力されている１日のみ出力対象となります。<br />
				&emsp;2.未出力のリストのみ出力したい場合は、リスト出力対象の「未出力のみ」を選択し、<br />
				&emsp;&emsp;各リストボタンを押してください。</font>
				<br />
				<br />
			</td>
			<td style="width: 3px; border-width: 0px;"></td>
		</tr>
		<tr style="height: 1px;"></tr>
	</table>
<!---- システム共通フッター END ------------------------------------------------------------------------------------------------------------------------------------------------->
</table>
</body>